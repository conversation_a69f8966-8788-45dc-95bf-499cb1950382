using System.ComponentModel.DataAnnotations;

namespace Aviation.Authentication.Api.Models;

public class User
{
    public Guid UserId { get; set; } = Guid.NewGuid();
    
    [Required]
    public string EmployeeId { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public Guid AzureAdObjectId { get; set; }
    
    [Required]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    public string LastName { get; set; } = string.Empty;
    
    public bool IsActive { get; set; } = true;
    public DateTime? LastLogin { get; set; }
    public int FailedAttempts { get; set; } = 0;
    public DateTime? LockoutEnd { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    public DateTime? ModifiedDate { get; set; }
    
    // Navigation properties
    public List<UserRole> UserRoles { get; set; } = new();
    public List<LoginHistory> LoginHistories { get; set; } = new();
    public List<PasswordReset> PasswordResets { get; set; } = new();
    
    // Computed properties
    public string FullName => $"{FirstName} {LastName}";
    public bool IsLockedOut => LockoutEnd.HasValue && LockoutEnd > DateTime.UtcNow;
}

public class PasswordReset
{
    public Guid RequestId { get; set; } = Guid.NewGuid();
    public Guid UserId { get; set; }
    public User User { get; set; } = null!;
    
    [Required]
    public string Token { get; set; } = string.Empty;
    
    public DateTime Expires { get; set; }
    public bool IsUsed { get; set; } = false;
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
}

public class LoginHistory
{
    public Guid LogId { get; set; } = Guid.NewGuid();
    public Guid? UserId { get; set; }
    public User? User { get; set; }
    
    public string? AttemptedEmail { get; set; }
    public string? AttemptedEmployeeId { get; set; }
    public DateTime LoginTime { get; set; } = DateTime.UtcNow;
    public string? IPAddress { get; set; }
    public bool Success { get; set; }
    public string? FailureReason { get; set; }
}

public class Role
{
    public Guid RoleId { get; set; } = Guid.NewGuid();
    
    [Required]
    public string RoleCode { get; set; } = string.Empty;
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    public DateTime? ModifiedDate { get; set; }
    
    // Navigation properties
    public List<UserRole> UserRoles { get; set; } = new();
    public List<RolePermission> RolePermissions { get; set; } = new();
}

public class UserRole
{
    public Guid UserRoleId { get; set; } = Guid.NewGuid();
    public Guid UserId { get; set; }
    public User User { get; set; } = null!;
    
    public Guid RoleId { get; set; }
    public Role Role { get; set; } = null!;
    
    public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
}

public class Entity
{
    public Guid EntityId { get; set; } = Guid.NewGuid();
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public List<RolePermission> RolePermissions { get; set; } = new();
}

public class Module
{
    public Guid ModuleId { get; set; } = Guid.NewGuid();
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public List<SubModule> SubModules { get; set; } = new();
    public List<RolePermission> RolePermissions { get; set; } = new();
}

public class SubModule
{
    public Guid SubModuleId { get; set; } = Guid.NewGuid();
    public Guid ModuleId { get; set; }
    public Module Module { get; set; } = null!;
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public List<RolePermission> RolePermissions { get; set; } = new();
}

public class Permission
{
    public Guid PermissionId { get; set; } = Guid.NewGuid();
    
    [Required]
    public string Name { get; set; } = string.Empty; // Create, View, Update, Delete
    
    public string? Description { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public List<RolePermission> RolePermissions { get; set; } = new();
}

public class RolePermission
{
    public Guid RolePermissionId { get; set; } = Guid.NewGuid();
    public Guid RoleId { get; set; }
    public Role Role { get; set; } = null!;
    
    public Guid? EntityId { get; set; }
    public Entity? Entity { get; set; }
    
    public Guid? ModuleId { get; set; }
    public Module? Module { get; set; }
    
    public Guid? SubModuleId { get; set; }
    public SubModule? SubModule { get; set; }
    
    public Guid PermissionId { get; set; }
    public Permission Permission { get; set; } = null!;
    
    public bool Granted { get; set; } = true;
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    public DateTime? ModifiedDate { get; set; }
}

// DTOs for API
public class CreateUserRequest
{
    [Required]
    public string EmployeeId { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public Guid AzureAdObjectId { get; set; }
    
    [Required]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    public string LastName { get; set; } = string.Empty;
    
    public List<Guid> RoleIds { get; set; } = new();
}

public class UpdateUserRequest
{
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Email { get; set; }
    public bool? IsActive { get; set; }
    public List<Guid>? RoleIds { get; set; }
}

public class UserResponse
{
    public Guid UserId { get; set; }
    public string EmployeeId { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime? LastLogin { get; set; }
    public bool IsLockedOut { get; set; }
    public DateTime CreatedDate { get; set; }
    public List<RoleResponse> Roles { get; set; } = new();
}

public class RoleResponse
{
    public Guid RoleId { get; set; }
    public string RoleCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public DateTime AssignedDate { get; set; }
}

public class UserLoginRequest
{
    [Required]
    public string EmailOrEmployeeId { get; set; } = string.Empty;
    
    public string? AzureAdToken { get; set; }
}

public class UserLoginResponse
{
    public string AccessToken { get; set; } = string.Empty;
    public string TokenType { get; set; } = "Bearer";
    public int ExpiresIn { get; set; }
    public UserResponse User { get; set; } = null!;
    public List<string> Permissions { get; set; } = new();
}
