using Aviation.Authentication.Api.Models;
using Aviation.Authentication.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Aviation.Authentication.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RolesController : ControllerBase
{
    private readonly IRoleService _roleService;
    private readonly IPermissionService _permissionService;
    private readonly IAuditService _auditService;
    private readonly ILogger<RolesController> _logger;

    public RolesController(
        IRoleService roleService,
        IPermissionService permissionService,
        IAuditService auditService,
        ILogger<RolesController> logger)
    {
        _roleService = roleService;
        _permissionService = permissionService;
        _auditService = auditService;
        _logger = logger;
    }

    /// <summary>
    /// Get all roles
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<Role>), 200)]
    public async Task<ActionResult<IEnumerable<Role>>> GetRoles([FromQuery] bool activeOnly = true)
    {
        var roles = await _roleService.GetRolesAsync(activeOnly);
        return Ok(roles);
    }

    /// <summary>
    /// Get role by ID
    /// </summary>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(Role), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<Role>> GetRole(Guid id)
    {
        var role = await _roleService.GetRoleAsync(id);
        if (role == null)
        {
            return NotFound($"Role with ID {id} not found");
        }

        return Ok(role);
    }

    /// <summary>
    /// Get role by code
    /// </summary>
    [HttpGet("by-code/{code}")]
    [ProducesResponseType(typeof(Role), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<Role>> GetRoleByCode(string code)
    {
        var role = await _roleService.GetRoleByCodeAsync(code);
        if (role == null)
        {
            return NotFound($"Role with code {code} not found");
        }

        return Ok(role);
    }

    /// <summary>
    /// Create a new role
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(Role), 201)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<Role>> CreateRole([FromBody] CreateRoleRequest request)
    {
        try
        {
            var role = await _roleService.CreateRoleAsync(request);

            await _auditService.LogAsync("role_created", "roles", 
                $"New role created: {request.Name}", null, GetClientIpAddress(), GetUserAgent());

            return CreatedAtAction(nameof(GetRole), new { id = role.RoleId }, role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role {RoleName}", request.Name);
            return StatusCode(500, "An error occurred while creating the role");
        }
    }

    /// <summary>
    /// Update an existing role
    /// </summary>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(Role), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<Role>> UpdateRole(Guid id, [FromBody] UpdateRoleRequest request)
    {
        try
        {
            var role = await _roleService.UpdateRoleAsync(id, request);
            if (role == null)
            {
                return NotFound($"Role with ID {id} not found");
            }

            await _auditService.LogAsync("role_updated", "roles", 
                $"Role updated: {role.Name}", null, GetClientIpAddress(), GetUserAgent());

            return Ok(role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role {RoleId}", id);
            return StatusCode(500, "An error occurred while updating the role");
        }
    }

    /// <summary>
    /// Delete a role
    /// </summary>
    [HttpDelete("{id}")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> DeleteRole(Guid id)
    {
        try
        {
            var success = await _roleService.DeleteRoleAsync(id);
            if (!success)
            {
                return NotFound($"Role with ID {id} not found");
            }

            await _auditService.LogAsync("role_deleted", "roles", 
                $"Role deleted: ID {id}", null, GetClientIpAddress(), GetUserAgent());

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role {RoleId}", id);
            return StatusCode(500, "An error occurred while deleting the role");
        }
    }

    /// <summary>
    /// Assign role to user
    /// </summary>
    [HttpPost("{roleId}/users/{userId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> AssignRoleToUser(Guid roleId, Guid userId)
    {
        try
        {
            var assignedBy = User.Identity?.Name ?? "api-admin";
            var success = await _roleService.AssignRoleToUserAsync(userId, roleId, assignedBy);
            
            if (!success)
            {
                return BadRequest("Role assignment failed. Role may already be assigned to user.");
            }

            await _auditService.LogAsync("role_assigned", "roles", 
                $"Role {roleId} assigned to user {userId}", null, GetClientIpAddress(), GetUserAgent());

            return Ok(new { message = "Role assigned successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role {RoleId} to user {UserId}", roleId, userId);
            return StatusCode(500, "An error occurred while assigning the role");
        }
    }

    /// <summary>
    /// Remove role from user
    /// </summary>
    [HttpDelete("{roleId}/users/{userId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> RemoveRoleFromUser(Guid roleId, Guid userId)
    {
        try
        {
            var success = await _roleService.RemoveRoleFromUserAsync(userId, roleId);
            
            if (!success)
            {
                return NotFound("Role assignment not found");
            }

            await _auditService.LogAsync("role_removed", "roles", 
                $"Role {roleId} removed from user {userId}", null, GetClientIpAddress(), GetUserAgent());

            return Ok(new { message = "Role removed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing role {RoleId} from user {UserId}", roleId, userId);
            return StatusCode(500, "An error occurred while removing the role");
        }
    }

    /// <summary>
    /// Get users in role
    /// </summary>
    [HttpGet("{id}/users")]
    [ProducesResponseType(typeof(IEnumerable<User>), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<IEnumerable<User>>> GetUsersInRole(Guid id)
    {
        var role = await _roleService.GetRoleAsync(id);
        if (role == null)
        {
            return NotFound($"Role with ID {id} not found");
        }

        var users = await _roleService.GetUsersInRoleAsync(id);
        return Ok(users);
    }

    /// <summary>
    /// Get role permissions
    /// </summary>
    [HttpGet("{id}/permissions")]
    [ProducesResponseType(typeof(IEnumerable<RolePermission>), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<IEnumerable<RolePermission>>> GetRolePermissions(Guid id)
    {
        var role = await _roleService.GetRoleAsync(id);
        if (role == null)
        {
            return NotFound($"Role with ID {id} not found");
        }

        var permissions = await _permissionService.GetRolePermissionsAsync(id);
        return Ok(permissions);
    }

    /// <summary>
    /// Grant permission to role
    /// </summary>
    [HttpPost("{id}/permissions")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> GrantPermissionToRole(Guid id, [FromBody] GrantPermissionRequest request)
    {
        try
        {
            var role = await _roleService.GetRoleAsync(id);
            if (role == null)
            {
                return NotFound($"Role with ID {id} not found");
            }

            var success = await _permissionService.GrantPermissionToRoleAsync(
                id, request.PermissionId, request.EntityId, request.ModuleId, request.SubModuleId);
            
            if (!success)
            {
                return BadRequest("Failed to grant permission to role");
            }

            await _auditService.LogAsync("permission_granted", "roles", 
                $"Permission {request.PermissionId} granted to role {id}", null, GetClientIpAddress(), GetUserAgent());

            return Ok(new { message = "Permission granted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error granting permission {PermissionId} to role {RoleId}", request.PermissionId, id);
            return StatusCode(500, "An error occurred while granting the permission");
        }
    }

    /// <summary>
    /// Revoke permission from role
    /// </summary>
    [HttpDelete("{id}/permissions/{rolePermissionId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> RevokePermissionFromRole(Guid id, Guid rolePermissionId)
    {
        try
        {
            var success = await _permissionService.RevokePermissionFromRoleAsync(rolePermissionId);
            
            if (!success)
            {
                return NotFound("Role permission not found");
            }

            await _auditService.LogAsync("permission_revoked", "roles", 
                $"Permission {rolePermissionId} revoked from role {id}", null, GetClientIpAddress(), GetUserAgent());

            return Ok(new { message = "Permission revoked successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking permission {RolePermissionId} from role {RoleId}", rolePermissionId, id);
            return StatusCode(500, "An error occurred while revoking the permission");
        }
    }

    private string GetClientIpAddress()
    {
        return Request.Headers["X-Forwarded-For"].FirstOrDefault() 
               ?? Request.Headers["X-Real-IP"].FirstOrDefault()
               ?? Request.HttpContext.Connection.RemoteIpAddress?.ToString() 
               ?? "unknown";
    }

    private string GetUserAgent()
    {
        return Request.Headers.UserAgent.ToString();
    }
}
