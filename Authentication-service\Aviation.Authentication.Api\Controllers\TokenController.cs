using Aviation.Authentication.Api.Models;
using Aviation.Authentication.Api.Services;
using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace Aviation.Authentication.Api.Controllers;

[ApiController]
[Route("oauth")]
public class TokenController : ControllerBase
{
    private readonly ITokenService _tokenService;
    private readonly IRateLimitService _rateLimitService;
    private readonly ILogger<TokenController> _logger;

    public TokenController(
        ITokenService tokenService,
        IRateLimitService rateLimitService,
        ILogger<TokenController> logger)
    {
        _tokenService = tokenService;
        _rateLimitService = rateLimitService;
        _logger = logger;
    }

    /// <summary>
    /// OAuth 2.0 Token Endpoint - Client Credentials Grant
    /// </summary>
    [HttpPost("token")]
    [Consumes("application/x-www-form-urlencoded")]
    [ProducesResponseType(typeof(TokenResponse), 200)]
    [ProducesResponseType(typeof(TokenErrorResponse), 400)]
    [ProducesResponseType(typeof(TokenErrorResponse), 401)]
    [ProducesResponseType(typeof(TokenErrorResponse), 429)]
    public async Task<IActionResult> Token([FromForm] TokenRequest request)
    {
        var ipAddress = GetClientIpAddress();
        var userAgent = Request.Headers.UserAgent.ToString();

        try
        {
            // Validate required parameters
            if (string.IsNullOrEmpty(request.GrantType) || 
                string.IsNullOrEmpty(request.ClientId) || 
                string.IsNullOrEmpty(request.ClientSecret))
            {
                return BadRequest(new TokenErrorResponse
                {
                    Error = "invalid_request",
                    ErrorDescription = "Missing required parameters: grant_type, client_id, or client_secret"
                });
            }

            // Check rate limiting
            if (!await _rateLimitService.IsAllowedAsync(request.ClientId, ipAddress))
            {
                var rateLimitInfo = await _rateLimitService.GetRateLimitInfoAsync(request.ClientId, ipAddress);
                
                Response.Headers.Add("X-RateLimit-Limit", rateLimitInfo.Limit.ToString());
                Response.Headers.Add("X-RateLimit-Remaining", "0");
                Response.Headers.Add("X-RateLimit-Reset", new DateTimeOffset(rateLimitInfo.ResetTime).ToUnixTimeSeconds().ToString());

                return StatusCode(429, new TokenErrorResponse
                {
                    Error = "rate_limit_exceeded",
                    ErrorDescription = "Rate limit exceeded. Please try again later."
                });
            }

            // Generate token
            var tokenResponse = await _tokenService.GenerateTokenAsync(request, ipAddress, userAgent);

            if (tokenResponse == null)
            {
                return Unauthorized(new TokenErrorResponse
                {
                    Error = "invalid_client",
                    ErrorDescription = "Invalid client credentials or client not authorized"
                });
            }

            // Increment rate limit usage
            await _rateLimitService.IncrementUsageAsync(request.ClientId, ipAddress);

            // Add rate limit headers
            var rateLimitInfoSuccess = await _rateLimitService.GetRateLimitInfoAsync(request.ClientId, ipAddress);
            Response.Headers.Add("X-RateLimit-Limit", rateLimitInfoSuccess.Limit.ToString());
            Response.Headers.Add("X-RateLimit-Remaining", rateLimitInfoSuccess.Remaining.ToString());
            Response.Headers.Add("X-RateLimit-Reset", new DateTimeOffset(rateLimitInfoSuccess.ResetTime).ToUnixTimeSeconds().ToString());

            return Ok(tokenResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing token request for client {ClientId}", request.ClientId);
            
            return StatusCode(500, new TokenErrorResponse
            {
                Error = "server_error",
                ErrorDescription = "An internal server error occurred"
            });
        }
    }

    /// <summary>
    /// Token Introspection Endpoint (RFC 7662)
    /// </summary>
    [HttpPost("introspect")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> Introspect([FromForm] string token, [FromForm] string? token_type_hint = null)
    {
        if (string.IsNullOrEmpty(token))
        {
            return BadRequest(new { error = "invalid_request", error_description = "Missing token parameter" });
        }

        try
        {
            var isValid = await _tokenService.ValidateTokenAsync(token);
            
            if (!isValid)
            {
                return Ok(new { active = false });
            }

            var claims = _tokenService.ValidateTokenAndGetClaims(token);
            if (claims == null)
            {
                return Ok(new { active = false });
            }

            // Return token introspection response
            return Ok(new 
            { 
                active = true,
                claims = claims
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token introspection");
            return Ok(new { active = false });
        }
    }

    /// <summary>
    /// Token Revocation Endpoint (RFC 7009)
    /// </summary>
    [HttpPost("revoke")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> Revoke([FromForm] string token, [FromForm] string? token_type_hint = null)
    {
        if (string.IsNullOrEmpty(token))
        {
            return BadRequest(new { error = "invalid_request", error_description = "Missing token parameter" });
        }

        try
        {
            // Extract token ID from JWT
            var tokenId = ExtractTokenId(token);
            if (string.IsNullOrEmpty(tokenId))
            {
                return BadRequest(new { error = "invalid_request", error_description = "Invalid token format" });
            }

            await _tokenService.RevokeTokenAsync(tokenId, "Revoked via API");
            
            // Always return 200 OK regardless of whether token was found (per RFC 7009)
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token revocation");
            return Ok(); // Still return 200 OK per RFC 7009
        }
    }

    /// <summary>
    /// JWKS (JSON Web Key Set) Endpoint
    /// </summary>
    [HttpGet(".well-known/jwks.json")]
    [ProducesResponseType(typeof(object), 200)]
    public IActionResult Jwks()
    {
        // In a production environment, you would return the public keys used to verify JWT signatures
        // For symmetric keys (HMAC), this endpoint might not be needed
        // For asymmetric keys (RSA/ECDSA), you would return the public key components
        
        return Ok(new 
        { 
            keys = new object[] 
            {
                // Add your public key information here
                // This is a placeholder - implement based on your key management strategy
            }
        });
    }

    /// <summary>
    /// OAuth 2.0 Discovery Endpoint (RFC 8414)
    /// </summary>
    [HttpGet(".well-known/oauth-authorization-server")]
    [ProducesResponseType(typeof(object), 200)]
    public IActionResult Discovery()
    {
        var baseUrl = $"{Request.Scheme}://{Request.Host}";
        
        return Ok(new
        {
            issuer = baseUrl,
            token_endpoint = $"{baseUrl}/oauth/token",
            introspection_endpoint = $"{baseUrl}/oauth/introspect",
            revocation_endpoint = $"{baseUrl}/oauth/revoke",
            jwks_uri = $"{baseUrl}/oauth/.well-known/jwks.json",
            grant_types_supported = new[] { "client_credentials" },
            token_endpoint_auth_methods_supported = new[] { "client_secret_basic", "client_secret_post" },
            introspection_endpoint_auth_methods_supported = new[] { "client_secret_basic", "client_secret_post" },
            revocation_endpoint_auth_methods_supported = new[] { "client_secret_basic", "client_secret_post" },
            scopes_supported = new[] 
            { 
                "partner:read", "partner:write", "customer:read", "customer:write",
                "order:read", "order:write", "order:cancel",
                "invoice:read", "invoice:write", "billing:read",
                "product:read", "product:write", "pricing:read", "pricing:write",
                "trip:estimate", "flight:read", "airport:read",
                "document:read", "document:write", "document:delete",
                "app:read", "app:write", "app:admin"
            }
        });
    }

    private string GetClientIpAddress()
    {
        var ipAddress = Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (string.IsNullOrEmpty(ipAddress))
        {
            ipAddress = Request.Headers["X-Real-IP"].FirstOrDefault();
        }
        if (string.IsNullOrEmpty(ipAddress))
        {
            ipAddress = Request.HttpContext.Connection.RemoteIpAddress?.ToString();
        }
        return ipAddress ?? "unknown";
    }

    private string? ExtractTokenId(string token)
    {
        try
        {
            var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);
            return jsonToken.Claims.FirstOrDefault(c => c.Type == "jti")?.Value;
        }
        catch
        {
            return null;
        }
    }
}
