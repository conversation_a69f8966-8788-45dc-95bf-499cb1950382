using Aviation.Authentication.Api.Data;
using Aviation.Authentication.Api.Models;
using Microsoft.EntityFrameworkCore;

namespace Aviation.Authentication.Api.Services;

public class PermissionService : IPermissionService
{
    private readonly AuthDbContext _context;
    private readonly ILogger<PermissionService> _logger;

    public PermissionService(AuthDbContext context, ILogger<PermissionService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<Permission>> GetPermissionsAsync()
    {
        return await _context.Permissions
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    public async Task<Permission?> GetPermissionAsync(Guid permissionId)
    {
        return await _context.Permissions.FindAsync(permissionId);
    }

    public async Task<Permission?> GetPermissionByNameAsync(string name)
    {
        return await _context.Permissions
            .FirstOrDefaultAsync(p => p.Name == name);
    }

    public async Task<Permission> CreatePermissionAsync(CreatePermissionRequest request)
    {
        var permission = new Permission
        {
            Name = request.Name,
            Description = request.Description,
            CreatedDate = DateTime.UtcNow
        };

        _context.Permissions.Add(permission);
        await _context.SaveChangesAsync();

        return permission;
    }

    public async Task<Permission?> UpdatePermissionAsync(Guid permissionId, UpdatePermissionRequest request)
    {
        var permission = await _context.Permissions.FindAsync(permissionId);
        if (permission == null)
            return null;

        if (!string.IsNullOrEmpty(request.Name))
            permission.Name = request.Name;

        if (!string.IsNullOrEmpty(request.Description))
            permission.Description = request.Description;

        await _context.SaveChangesAsync();
        return permission;
    }

    public async Task<bool> DeletePermissionAsync(Guid permissionId)
    {
        var permission = await _context.Permissions.FindAsync(permissionId);
        if (permission == null)
            return false;

        // Check if permission is in use
        var isInUse = await _context.RolePermissions.AnyAsync(rp => rp.PermissionId == permissionId);
        if (isInUse)
        {
            throw new InvalidOperationException("Cannot delete permission that is currently assigned to roles");
        }

        _context.Permissions.Remove(permission);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> GrantPermissionToRoleAsync(Guid roleId, Guid permissionId, Guid? entityId = null, Guid? moduleId = null, Guid? subModuleId = null)
    {
        // Check if permission already exists for this role
        var existingPermission = await _context.RolePermissions
            .FirstOrDefaultAsync(rp => rp.RoleId == roleId && 
                                      rp.PermissionId == permissionId &&
                                      rp.EntityId == entityId &&
                                      rp.ModuleId == moduleId &&
                                      rp.SubModuleId == subModuleId);

        if (existingPermission != null)
        {
            existingPermission.Granted = true;
            existingPermission.ModifiedDate = DateTime.UtcNow;
        }
        else
        {
            var rolePermission = new RolePermission
            {
                RoleId = roleId,
                PermissionId = permissionId,
                EntityId = entityId,
                ModuleId = moduleId,
                SubModuleId = subModuleId,
                Granted = true,
                CreatedDate = DateTime.UtcNow
            };

            _context.RolePermissions.Add(rolePermission);
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> RevokePermissionFromRoleAsync(Guid rolePermissionId)
    {
        var rolePermission = await _context.RolePermissions.FindAsync(rolePermissionId);
        if (rolePermission == null)
            return false;

        rolePermission.Granted = false;
        rolePermission.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<RolePermission>> GetRolePermissionsAsync(Guid roleId)
    {
        return await _context.RolePermissions
            .Where(rp => rp.RoleId == roleId)
            .Include(rp => rp.Permission)
            .Include(rp => rp.Entity)
            .Include(rp => rp.Module)
            .Include(rp => rp.SubModule)
            .ToListAsync();
    }

    public async Task<List<string>> GetEffectivePermissionsAsync(Guid userId)
    {
        var permissions = await _context.UserRoles
            .Where(ur => ur.UserId == userId)
            .Join(_context.RolePermissions,
                ur => ur.RoleId,
                rp => rp.RoleId,
                (ur, rp) => rp)
            .Where(rp => rp.Granted)
            .Include(rp => rp.Permission)
            .Include(rp => rp.Entity)
            .Include(rp => rp.Module)
            .Include(rp => rp.SubModule)
            .Select(rp => new
            {
                Permission = rp.Permission.Name,
                Entity = rp.Entity != null ? rp.Entity.Name : null,
                Module = rp.Module != null ? rp.Module.Name : null,
                SubModule = rp.SubModule != null ? rp.SubModule.Name : null
            })
            .ToListAsync();

        var permissionStrings = new List<string>();

        foreach (var perm in permissions)
        {
            var permissionKey = perm.Permission;
            
            if (!string.IsNullOrEmpty(perm.Entity))
                permissionKey += $":{perm.Entity}";
            
            if (!string.IsNullOrEmpty(perm.Module))
                permissionKey += $":{perm.Module}";
            
            if (!string.IsNullOrEmpty(perm.SubModule))
                permissionKey += $":{perm.SubModule}";

            permissionStrings.Add(permissionKey);
            
            // Also add the base permission without context
            if (!permissionStrings.Contains(perm.Permission))
                permissionStrings.Add(perm.Permission);
        }

        return permissionStrings.Distinct().ToList();
    }
}

public class RoleService : IRoleService
{
    private readonly AuthDbContext _context;
    private readonly ILogger<RoleService> _logger;

    public RoleService(AuthDbContext context, ILogger<RoleService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Role?> GetRoleAsync(Guid roleId)
    {
        return await _context.Roles
            .Include(r => r.RolePermissions)
            .ThenInclude(rp => rp.Permission)
            .FirstOrDefaultAsync(r => r.RoleId == roleId);
    }

    public async Task<Role?> GetRoleByCodeAsync(string roleCode)
    {
        return await _context.Roles
            .Include(r => r.RolePermissions)
            .ThenInclude(rp => rp.Permission)
            .FirstOrDefaultAsync(r => r.RoleCode == roleCode);
    }

    public async Task<IEnumerable<Role>> GetRolesAsync(bool activeOnly = true)
    {
        var query = _context.Roles.AsQueryable();
        
        if (activeOnly)
            query = query.Where(r => r.IsActive);

        return await query
            .OrderBy(r => r.Name)
            .ToListAsync();
    }

    public async Task<Role> CreateRoleAsync(CreateRoleRequest request)
    {
        // Generate role code
        var roleCode = await GenerateRoleCodeAsync();

        var role = new Role
        {
            RoleCode = roleCode,
            Name = request.Name,
            Description = request.Description,
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        };

        _context.Roles.Add(role);
        await _context.SaveChangesAsync();

        return role;
    }

    public async Task<Role?> UpdateRoleAsync(Guid roleId, UpdateRoleRequest request)
    {
        var role = await _context.Roles.FindAsync(roleId);
        if (role == null)
            return null;

        if (!string.IsNullOrEmpty(request.Name))
            role.Name = request.Name;

        if (!string.IsNullOrEmpty(request.Description))
            role.Description = request.Description;

        if (request.IsActive.HasValue)
            role.IsActive = request.IsActive.Value;

        role.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return role;
    }

    public async Task<bool> DeleteRoleAsync(Guid roleId)
    {
        var role = await _context.Roles.FindAsync(roleId);
        if (role == null)
            return false;

        // Check if role is assigned to users
        var isAssigned = await _context.UserRoles.AnyAsync(ur => ur.RoleId == roleId);
        if (isAssigned)
        {
            // Soft delete - deactivate instead of removing
            role.IsActive = false;
            role.ModifiedDate = DateTime.UtcNow;
        }
        else
        {
            // Hard delete if not assigned
            _context.Roles.Remove(role);
        }

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> AssignRoleToUserAsync(Guid userId, Guid roleId, string assignedBy)
    {
        // Check if assignment already exists
        var existingAssignment = await _context.UserRoles
            .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId);

        if (existingAssignment != null)
            return false; // Already assigned

        var userRole = new UserRole
        {
            UserId = userId,
            RoleId = roleId,
            AssignedDate = DateTime.UtcNow
        };

        _context.UserRoles.Add(userRole);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> RemoveRoleFromUserAsync(Guid userId, Guid roleId)
    {
        var userRole = await _context.UserRoles
            .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId);

        if (userRole == null)
            return false;

        _context.UserRoles.Remove(userRole);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<IEnumerable<User>> GetUsersInRoleAsync(Guid roleId)
    {
        return await _context.UserRoles
            .Where(ur => ur.RoleId == roleId)
            .Include(ur => ur.User)
            .Select(ur => ur.User)
            .ToListAsync();
    }

    public async Task<IEnumerable<Role>> GetUserRolesAsync(Guid userId)
    {
        return await _context.UserRoles
            .Where(ur => ur.UserId == userId)
            .Include(ur => ur.Role)
            .Select(ur => ur.Role)
            .ToListAsync();
    }

    private async Task<string> GenerateRoleCodeAsync()
    {
        var lastRole = await _context.Roles
            .OrderByDescending(r => r.RoleCode)
            .FirstOrDefaultAsync();

        if (lastRole == null)
            return "ROLE-001";

        // Extract number from last role code
        var lastCodeNumber = int.Parse(lastRole.RoleCode.Substring(5));
        var newCodeNumber = lastCodeNumber + 1;

        return $"ROLE-{newCodeNumber:D3}";
    }
}
