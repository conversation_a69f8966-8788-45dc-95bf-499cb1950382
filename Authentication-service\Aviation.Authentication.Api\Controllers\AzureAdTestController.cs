using Microsoft.AspNetCore.Mvc;
using Aviation.Authentication.Api.Services;
using Microsoft.Extensions.Options;

namespace Aviation.Authentication.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AzureAdTestController : ControllerBase
{
    private readonly IAzureAdService _azureAdService;
    private readonly ILogger<AzureAdTestController> _logger;
    private readonly AzureAdConfiguration _azureAdConfig;

    public AzureAdTestController(IAzureAdService azureAdService, ILogger<AzureAdTestController> logger, IOptions<AzureAdConfiguration> azureAdOptions)
    {
        _azureAdService = azureAdService;
        _logger = logger;
        _azureAdConfig = azureAdOptions.Value;
    }

    [HttpGet("config")]
    public IActionResult GetConfig()
    {
        try
        {
            _logger.LogInformation("Getting Azure AD configuration...");

            return Ok(new
            {
                Status = "Success",
                TenantId = _azureAdConfig.TenantId,
                ClientId = _azureAdConfig.ClientId,
                HasClientSecret = !string.IsNullOrEmpty(_azureAdConfig.ClientSecret),
                Instance = _azureAdConfig.Instance,
                GraphApiUrl = _azureAdConfig.GraphApiUrl,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Azure AD configuration test failed");
            return Ok(new
            {
                Status = "Error",
                Message = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    [HttpGet("test-service")]
    public IActionResult TestService()
    {
        try
        {
            _logger.LogInformation("Testing Azure AD service instantiation...");

            // Test if the service is properly instantiated
            var serviceType = _azureAdService.GetType().Name;

            return Ok(new
            {
                Status = "Success",
                Message = "Azure AD service is properly instantiated",
                ServiceType = serviceType,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Azure AD service test failed");
            return Ok(new
            {
                Status = "Error",
                Message = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    [HttpGet("test-connection")]
    public async Task<IActionResult> TestConnection()
    {
        try
        {
            _logger.LogInformation("Testing Azure AD connection...");

            // Try to get users (this will test the Graph API connection)
            var users = await _azureAdService.GetUsersAsync();
            var userCount = users.Count();

            return Ok(new
            {
                Status = "Success",
                Message = "Azure AD connection is working",
                UserCount = userCount,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Azure AD connection test failed");
            return Ok(new
            {
                Status = "Error",
                Message = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    [HttpGet("validate-token/{token}")]
    public async Task<IActionResult> ValidateToken(string token)
    {
        try
        {
            _logger.LogInformation("Testing Azure AD token validation...");
            
            var isValid = await _azureAdService.ValidateTokenAsync(token);
            
            return Ok(new
            {
                Status = "Success",
                IsValid = isValid,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Azure AD token validation test failed");
            return Ok(new
            {
                Status = "Error",
                Message = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Get all Azure AD users
    /// </summary>
    [HttpGet("users")]
    [ProducesResponseType(typeof(object), 200)]
    public async Task<IActionResult> GetAllUsers([FromQuery] string? filter = null, [FromQuery] int? top = null)
    {
        try
        {
            _logger.LogInformation("Getting all Azure AD users...");

            // Build filter string
            var filterString = filter ?? "accountEnabled eq true";

            var users = await _azureAdService.GetUsersAsync(filterString);

            var userList = users.Select(user => new
            {
                ObjectId = user.ObjectId,
                DisplayName = user.DisplayName,
                Email = user.Email,
                UserPrincipalName = user.UserPrincipalName,
                FirstName = user.GivenName,
                LastName = user.Surname,
                JobTitle = user.JobTitle,
                Department = user.Department,
                OfficeLocation = user.OfficeLocation,
                AccountEnabled = user.AccountEnabled,
                EmployeeId = user.EmployeeId
            }).ToList();

            // Apply top limit if specified
            if (top.HasValue && top.Value > 0)
            {
                userList = userList.Take(top.Value).ToList();
            }

            return Ok(new
            {
                Status = "Success",
                Message = $"Retrieved {userList.Count} users from Azure AD",
                TotalUsers = userList.Count,
                Filter = filterString,
                Users = userList,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Azure AD users");
            return Ok(new
            {
                Status = "Error",
                Message = ex.Message,
                TotalUsers = 0,
                Users = new List<object>(),
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Get Azure AD users with detailed information
    /// </summary>
    [HttpGet("users/detailed")]
    [ProducesResponseType(typeof(object), 200)]
    public async Task<IActionResult> GetAllUsersDetailed([FromQuery] string? filter = null)
    {
        try
        {
            _logger.LogInformation("Getting detailed Azure AD users...");

            var filterString = filter ?? "accountEnabled eq true";
            var users = await _azureAdService.GetUsersAsync(filterString);

            var detailedUsers = new List<object>();

            foreach (var user in users)
            {
                try
                {
                    // Get user groups
                    var groups = await _azureAdService.GetUserGroupsAsync(user.ObjectId);

                    detailedUsers.Add(new
                    {
                        ObjectId = user.ObjectId,
                        DisplayName = user.DisplayName,
                        Email = user.Email,
                        UserPrincipalName = user.UserPrincipalName,
                        FirstName = user.GivenName,
                        LastName = user.Surname,
                        JobTitle = user.JobTitle,
                        Department = user.Department,
                        OfficeLocation = user.OfficeLocation,
                        AccountEnabled = user.AccountEnabled,
                        EmployeeId = user.EmployeeId,
                        Groups = groups.ToList()
                    });
                }
                catch (Exception userEx)
                {
                    _logger.LogWarning(userEx, "Error getting details for user {UserId}", user.ObjectId);
                    detailedUsers.Add(new
                    {
                        ObjectId = user.ObjectId,
                        DisplayName = user.DisplayName,
                        Email = user.Email,
                        UserPrincipalName = user.UserPrincipalName,
                        FirstName = user.GivenName,
                        LastName = user.Surname,
                        JobTitle = user.JobTitle,
                        Department = user.Department,
                        OfficeLocation = user.OfficeLocation,
                        AccountEnabled = user.AccountEnabled,
                        EmployeeId = user.EmployeeId,
                        Groups = new List<string>(),
                        Error = "Could not retrieve groups"
                    });
                }
            }

            return Ok(new
            {
                Status = "Success",
                Message = $"Retrieved {detailedUsers.Count} detailed users from Azure AD",
                TotalUsers = detailedUsers.Count,
                Filter = filterString,
                Users = detailedUsers,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting detailed Azure AD users");
            return Ok(new
            {
                Status = "Error",
                Message = ex.Message,
                TotalUsers = 0,
                Users = new List<object>(),
                Timestamp = DateTime.UtcNow
            });
        }
    }
}
