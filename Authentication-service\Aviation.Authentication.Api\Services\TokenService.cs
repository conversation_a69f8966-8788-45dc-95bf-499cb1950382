using Aviation.Authentication.Api.Data;
using Aviation.Authentication.Api.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace Aviation.Authentication.Api.Services;

public class TokenService : ITokenService
{
    private readonly AuthDbContext _context;
    private readonly IClientService _clientService;
    private readonly IAuditService _auditService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<TokenService> _logger;

    public TokenService(
        AuthDbContext context,
        IClientService clientService,
        IAuditService auditService,
        IConfiguration configuration,
        ILogger<TokenService> logger)
    {
        _context = context;
        _clientService = clientService;
        _auditService = auditService;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<TokenResponse?> GenerateTokenAsync(TokenRequest request, string ipAddress, string userAgent)
    {
        try
        {
            // Validate grant type
            if (request.GrantType != "client_credentials")
            {
                await _auditService.LogAsync("token_request", "oauth/token", 
                    $"Invalid grant type: {request.GrantType}", null, ipAddress, userAgent, false, "Invalid grant type");
                return null;
            }

            // Validate client credentials
            if (!await _clientService.ValidateClientCredentialsAsync(request.ClientId, request.ClientSecret))
            {
                await _auditService.LogAsync("token_request", "oauth/token", 
                    $"Invalid client credentials for: {request.ClientId}", null, ipAddress, userAgent, false, "Invalid client credentials");
                return null;
            }

            var client = await _clientService.GetClientAsync(request.ClientId);
            if (client == null || client.Status != ClientStatus.Active)
            {
                await _auditService.LogAsync("token_request", "oauth/token", 
                    $"Client not found or inactive: {request.ClientId}", null, ipAddress, userAgent, false, "Client not found or inactive");
                return null;
            }

            // Validate requested scopes
            var requestedScopes = string.IsNullOrEmpty(request.Scope) 
                ? await _clientService.GetClientScopesAsync(request.ClientId)
                : request.Scope.Split(' ');

            var clientScopes = await _clientService.GetClientScopesAsync(request.ClientId);
            var validScopes = requestedScopes.Where(s => clientScopes.Contains(s)).ToList();

            if (!validScopes.Any())
            {
                await _auditService.LogAsync("token_request", "oauth/token", 
                    $"No valid scopes for client: {request.ClientId}", client.Id, ipAddress, userAgent, false, "No valid scopes");
                return null;
            }

            // Generate JWT token
            var tokenId = Guid.NewGuid().ToString();
            var issuedAt = DateTime.UtcNow;
            var expiresAt = issuedAt.AddSeconds(client.AccessTokenLifetimeSeconds);

            var token = GenerateJwtToken(client, validScopes, tokenId, issuedAt, expiresAt);
            var tokenHash = ComputeTokenHash(token);

            // Store token in database
            var accessToken = new AccessToken
            {
                TokenId = tokenId,
                ClientId = client.Id,
                TokenHash = tokenHash,
                IssuedAt = issuedAt,
                ExpiresAt = expiresAt,
                IpAddress = ipAddress,
                UserAgent = userAgent,
                Scopes = JsonSerializer.Serialize(validScopes)
            };

            _context.AccessTokens.Add(accessToken);
            await _context.SaveChangesAsync();

            // Update client last used
            await _clientService.UpdateLastUsedAsync(request.ClientId);

            // Log successful token generation
            await _auditService.LogAsync("token_generated", "oauth/token", 
                $"Token generated for client: {request.ClientId}, scopes: {string.Join(", ", validScopes)}", 
                client.Id, ipAddress, userAgent, true);

            return new TokenResponse
            {
                AccessToken = token,
                TokenType = "Bearer",
                ExpiresIn = client.AccessTokenLifetimeSeconds,
                Scope = string.Join(" ", validScopes),
                TokenId = tokenId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating token for client {ClientId}", request.ClientId);
            await _auditService.LogAsync("token_request", "oauth/token", 
                $"Error generating token for client: {request.ClientId}", null, ipAddress, userAgent, false, ex.Message);
            return null;
        }
    }

    public async Task<bool> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_configuration["JWT:SecretKey"] ?? throw new InvalidOperationException("JWT secret key not configured"));

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _configuration["JWT:Issuer"],
                ValidateAudience = true,
                ValidAudience = _configuration["JWT:Audience"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
            
            if (validatedToken is JwtSecurityToken jwtToken)
            {
                var jti = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti)?.Value;
                if (!string.IsNullOrEmpty(jti))
                {
                    // Check if token is revoked
                    var storedToken = await _context.AccessTokens
                        .FirstOrDefaultAsync(t => t.TokenId == jti && !t.IsRevoked);
                    
                    return storedToken != null && storedToken.ExpiresAt > DateTime.UtcNow;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Token validation failed");
            return false;
        }
    }

    public async Task<bool> RevokeTokenAsync(string tokenId, string reason)
    {
        try
        {
            var token = await _context.AccessTokens
                .FirstOrDefaultAsync(t => t.TokenId == tokenId && !t.IsRevoked);

            if (token != null)
            {
                token.IsRevoked = true;
                token.RevokedAt = DateTime.UtcNow;
                token.RevokedReason = reason;

                await _context.SaveChangesAsync();

                await _auditService.LogAsync("token_revoked", "oauth/token", 
                    $"Token revoked: {tokenId}, reason: {reason}", token.ClientId);

                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking token {TokenId}", tokenId);
            return false;
        }
    }

    public async Task CleanupExpiredTokensAsync()
    {
        try
        {
            var expiredTokens = await _context.AccessTokens
                .Where(t => t.ExpiresAt < DateTime.UtcNow)
                .ToListAsync();

            if (expiredTokens.Any())
            {
                _context.AccessTokens.RemoveRange(expiredTokens);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Cleaned up {Count} expired tokens", expiredTokens.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired tokens");
        }
    }

    public string? ValidateTokenAndGetClaims(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_configuration["JWT:SecretKey"] ?? throw new InvalidOperationException("JWT secret key not configured"));

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _configuration["JWT:Issuer"],
                ValidateAudience = true,
                ValidAudience = _configuration["JWT:Audience"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
            
            if (validatedToken is JwtSecurityToken jwtToken)
            {
                return JsonSerializer.Serialize(jwtToken.Claims.ToDictionary(c => c.Type, c => c.Value));
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    private string GenerateJwtToken(Client client, IEnumerable<string> scopes, string tokenId, DateTime issuedAt, DateTime expiresAt)
    {
        var key = Encoding.UTF8.GetBytes(_configuration["JWT:SecretKey"] ?? throw new InvalidOperationException("JWT secret key not configured"));
        var tokenHandler = new JwtSecurityTokenHandler();

        var claims = new List<Claim>
        {
            new(JwtRegisteredClaimNames.Jti, tokenId),
            new(JwtRegisteredClaimNames.Iat, new DateTimeOffset(issuedAt).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new(JwtRegisteredClaimNames.Exp, new DateTimeOffset(expiresAt).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new("client_id", client.ClientId),
            new("client_name", client.ClientName),
            new("client_type", client.ClientType.ToString())
        };

        // Add scopes as individual claims
        foreach (var scope in scopes)
        {
            claims.Add(new Claim("scope", scope));
        }

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = expiresAt,
            Issuer = _configuration["JWT:Issuer"],
            Audience = _configuration["JWT:Audience"],
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    private static string ComputeTokenHash(string token)
    {
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(token));
        return Convert.ToBase64String(hashBytes);
    }
}
