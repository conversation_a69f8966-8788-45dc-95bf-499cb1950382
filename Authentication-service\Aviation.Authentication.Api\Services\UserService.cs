using Aviation.Authentication.Api.Data;
using Aviation.Authentication.Api.Models;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace Aviation.Authentication.Api.Services;

public class UserService : IUserService
{
    private readonly AuthDbContext _context;
    private readonly IPermissionService _permissionService;
    private readonly IAuditService _auditService;
    private readonly ILogger<UserService> _logger;

    public UserService(
        AuthDbContext context,
        IPermissionService permissionService,
        IAuditService auditService,
        ILogger<UserService> logger)
    {
        _context = context;
        _permissionService = permissionService;
        _auditService = auditService;
        _logger = logger;
    }

    public async Task<User?> GetUserAsync(Guid userId)
    {
        return await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.UserId == userId);
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        return await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Email == email);
    }

    public async Task<User?> GetUserByEmployeeIdAsync(string employeeId)
    {
        return await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.EmployeeId == employeeId);
    }

    public async Task<User?> GetUserByAzureAdObjectIdAsync(Guid azureAdObjectId)
    {
        return await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.AzureAdObjectId == azureAdObjectId);
    }

    public async Task<IEnumerable<User>> GetUsersAsync(int page = 1, int pageSize = 20)
    {
        return await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .OrderBy(u => u.LastName)
            .ThenBy(u => u.FirstName)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<UserResponse> CreateUserAsync(CreateUserRequest request, string createdBy)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        
        try
        {
            // Check if user already exists
            var existingUser = await _context.Users
                .FirstOrDefaultAsync(u => u.Email == request.Email || u.EmployeeId == request.EmployeeId);
            
            if (existingUser != null)
            {
                throw new InvalidOperationException("User with this email or employee ID already exists");
            }

            var user = new User
            {
                EmployeeId = request.EmployeeId,
                Email = request.Email,
                AzureAdObjectId = request.AzureAdObjectId,
                FirstName = request.FirstName,
                LastName = request.LastName,
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Assign roles
            foreach (var roleId in request.RoleIds)
            {
                var role = await _context.Roles.FindAsync(roleId);
                if (role != null && role.IsActive)
                {
                    var userRole = new UserRole
                    {
                        UserId = user.UserId,
                        RoleId = roleId,
                        AssignedDate = DateTime.UtcNow
                    };
                    _context.UserRoles.Add(userRole);
                }
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            // Log user creation
            await _auditService.LogAsync("user_created", "users", 
                $"User created: {user.FullName} ({user.EmployeeId})", null, "", "", true);

            // Return user response
            var roles = await _context.UserRoles
                .Where(ur => ur.UserId == user.UserId)
                .Include(ur => ur.Role)
                .Select(ur => new RoleResponse
                {
                    RoleId = ur.Role.RoleId,
                    RoleCode = ur.Role.RoleCode,
                    Name = ur.Role.Name,
                    Description = ur.Role.Description,
                    IsActive = ur.Role.IsActive,
                    AssignedDate = ur.AssignedDate
                })
                .ToListAsync();

            return new UserResponse
            {
                UserId = user.UserId,
                EmployeeId = user.EmployeeId,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                FullName = user.FullName,
                IsActive = user.IsActive,
                LastLogin = user.LastLogin,
                IsLockedOut = user.IsLockedOut,
                CreatedDate = user.CreatedDate,
                Roles = roles
            };
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error creating user {Email}", request.Email);
            throw;
        }
    }

    public async Task<UserResponse?> UpdateUserAsync(Guid userId, UpdateUserRequest request)
    {
        var user = await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.UserId == userId);

        if (user == null)
            return null;

        // Update properties
        if (!string.IsNullOrEmpty(request.FirstName))
            user.FirstName = request.FirstName;
        
        if (!string.IsNullOrEmpty(request.LastName))
            user.LastName = request.LastName;
        
        if (!string.IsNullOrEmpty(request.Email))
            user.Email = request.Email;
        
        if (request.IsActive.HasValue)
            user.IsActive = request.IsActive.Value;

        user.ModifiedDate = DateTime.UtcNow;

        // Update roles if provided
        if (request.RoleIds != null)
        {
            // Remove existing roles
            var existingRoles = user.UserRoles.ToList();
            _context.UserRoles.RemoveRange(existingRoles);

            // Add new roles
            foreach (var roleId in request.RoleIds)
            {
                var role = await _context.Roles.FindAsync(roleId);
                if (role != null && role.IsActive)
                {
                    var userRole = new UserRole
                    {
                        UserId = userId,
                        RoleId = roleId,
                        AssignedDate = DateTime.UtcNow
                    };
                    _context.UserRoles.Add(userRole);
                }
            }
        }

        await _context.SaveChangesAsync();

        // Log user update
        await _auditService.LogAsync("user_updated", "users", 
            $"User updated: {user.FullName} ({user.EmployeeId})", null, "", "", true);

        // Return updated user response
        var roles = await _context.UserRoles
            .Where(ur => ur.UserId == userId)
            .Include(ur => ur.Role)
            .Select(ur => new RoleResponse
            {
                RoleId = ur.Role.RoleId,
                RoleCode = ur.Role.RoleCode,
                Name = ur.Role.Name,
                Description = ur.Role.Description,
                IsActive = ur.Role.IsActive,
                AssignedDate = ur.AssignedDate
            })
            .ToListAsync();

        return new UserResponse
        {
            UserId = user.UserId,
            EmployeeId = user.EmployeeId,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            IsActive = user.IsActive,
            LastLogin = user.LastLogin,
            IsLockedOut = user.IsLockedOut,
            CreatedDate = user.CreatedDate,
            Roles = roles
        };
    }

    public async Task<bool> DeleteUserAsync(Guid userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
            return false;

        // Soft delete - deactivate instead of removing
        user.IsActive = false;
        user.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        // Log user deletion
        await _auditService.LogAsync("user_deleted", "users", 
            $"User deleted: {user.FullName} ({user.EmployeeId})", null, "", "", true);

        return true;
    }

    public async Task<bool> ActivateUserAsync(Guid userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
            return false;

        user.IsActive = true;
        user.LockoutEnd = null;
        user.FailedAttempts = 0;
        user.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        await _auditService.LogAsync("user_activated", "users", 
            $"User activated: {user.FullName} ({user.EmployeeId})", null, "", "", true);

        return true;
    }

    public async Task<bool> DeactivateUserAsync(Guid userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
            return false;

        user.IsActive = false;
        user.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        await _auditService.LogAsync("user_deactivated", "users", 
            $"User deactivated: {user.FullName} ({user.EmployeeId})", null, "", "", true);

        return true;
    }

    public async Task<bool> LockUserAsync(Guid userId, TimeSpan lockoutDuration, string reason)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
            return false;

        user.LockoutEnd = DateTime.UtcNow.Add(lockoutDuration);
        user.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        await _auditService.LogAsync("user_locked", "users", 
            $"User locked: {user.FullName} ({user.EmployeeId}), reason: {reason}", null, "", "", true);

        return true;
    }

    public async Task<bool> UnlockUserAsync(Guid userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
            return false;

        user.LockoutEnd = null;
        user.FailedAttempts = 0;
        user.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        await _auditService.LogAsync("user_unlocked", "users", 
            $"User unlocked: {user.FullName} ({user.EmployeeId})", null, "", "", true);

        return true;
    }

    public async Task<UserLoginResponse?> AuthenticateUserAsync(UserLoginRequest request, string ipAddress, string userAgent)
    {
        // This is a placeholder for Azure AD integration
        // In a real implementation, you would validate the Azure AD token
        
        User? user = null;
        
        // Try to find user by email or employee ID
        if (request.EmailOrEmployeeId.Contains("@"))
        {
            user = await GetUserByEmailAsync(request.EmailOrEmployeeId);
        }
        else
        {
            user = await GetUserByEmployeeIdAsync(request.EmailOrEmployeeId);
        }

        if (user == null || !user.IsActive || user.IsLockedOut)
        {
            // Determine if input is email or employee ID for proper logging
            string? attemptedEmail = request.EmailOrEmployeeId.Contains("@") ? request.EmailOrEmployeeId : null;
            string? attemptedEmployeeId = !request.EmailOrEmployeeId.Contains("@") ? request.EmailOrEmployeeId : null;

            await LogLoginAttemptAsync(user?.UserId, attemptedEmail, attemptedEmployeeId, ipAddress, false,
                user == null ? "User not found" : !user.IsActive ? "User inactive" : "User locked out");
            return null;
        }

        // Update last login
        user.LastLogin = DateTime.UtcNow;
        user.FailedAttempts = 0;
        await _context.SaveChangesAsync();

        // Get user permissions
        var permissions = await GetUserPermissionsAsync(user.UserId);

        // Log successful login
        await LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, true);

        // Generate JWT token (this would be implemented in TokenService)
        // For now, return a placeholder token
        var userResponse = new UserResponse
        {
            UserId = user.UserId,
            EmployeeId = user.EmployeeId,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            IsActive = user.IsActive,
            LastLogin = user.LastLogin,
            IsLockedOut = user.IsLockedOut,
            CreatedDate = user.CreatedDate,
            Roles = user.UserRoles.Select(ur => new RoleResponse
            {
                RoleId = ur.Role.RoleId,
                RoleCode = ur.Role.RoleCode,
                Name = ur.Role.Name,
                Description = ur.Role.Description,
                IsActive = ur.Role.IsActive,
                AssignedDate = ur.AssignedDate
            }).ToList()
        };

        return new UserLoginResponse
        {
            AccessToken = "placeholder-jwt-token", // This would be generated by TokenService
            TokenType = "Bearer",
            ExpiresIn = 3600,
            User = userResponse,
            Permissions = permissions
        };
    }

    public async Task<List<string>> GetUserPermissionsAsync(Guid userId)
    {
        return await _permissionService.GetEffectivePermissionsAsync(userId);
    }

    public async Task<bool> HasPermissionAsync(Guid userId, string permission, string? entity = null, string? module = null, string? subModule = null)
    {
        var userPermissions = await GetUserPermissionsAsync(userId);
        
        // Simple permission check - in a real implementation, you might want more sophisticated logic
        var permissionKey = $"{permission}";
        if (!string.IsNullOrEmpty(entity))
            permissionKey += $":{entity}";
        if (!string.IsNullOrEmpty(module))
            permissionKey += $":{module}";
        if (!string.IsNullOrEmpty(subModule))
            permissionKey += $":{subModule}";

        return userPermissions.Contains(permissionKey) || userPermissions.Contains(permission);
    }

    public async Task LogLoginAttemptAsync(Guid? userId, string? email, string? employeeId, string ipAddress, bool success, string? failureReason = null)
    {
        var loginHistory = new LoginHistory
        {
            UserId = userId,
            AttemptedEmail = email,
            AttemptedEmployeeId = employeeId,
            LoginTime = DateTime.UtcNow,
            IPAddress = ipAddress,
            Success = success,
            FailureReason = failureReason
        };

        _context.LoginHistories.Add(loginHistory);
        await _context.SaveChangesAsync();

        // If failed login, increment failed attempts
        if (!success && userId.HasValue)
        {
            var user = await _context.Users.FindAsync(userId.Value);
            if (user != null)
            {
                user.FailedAttempts++;
                
                // Lock user after 5 failed attempts
                if (user.FailedAttempts >= 5)
                {
                    user.LockoutEnd = DateTime.UtcNow.AddMinutes(30);
                }
                
                await _context.SaveChangesAsync();
            }
        }
    }
}
