using System.ComponentModel.DataAnnotations;

namespace Aviation.Authentication.Api.Models;

public class Client
{
    public int Id { get; set; }
    
    [Required]
    public string ClientId { get; set; } = string.Empty;
    
    [Required]
    public string ClientSecret { get; set; } = string.Empty; // Hashed
    
    [Required]
    public string ClientName { get; set; } = string.Empty;
    
    public string Description { get; set; } = string.Empty;
    
    public ClientType ClientType { get; set; }
    
    public ClientStatus Status { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    
    public string CreatedBy { get; set; } = string.Empty;
    
    // Token settings
    public int AccessTokenLifetimeSeconds { get; set; } = 3600; // 1 hour default
    
    // Rate limiting
    public int RateLimitPerHour { get; set; } = 1000;
    
    // IP restrictions
    public string? AllowedIpAddresses { get; set; }
    
    // Webhook settings
    public string? WebhookUrl { get; set; }
    public string? WebhookSecret { get; set; }
    
    // Navigation properties
    public List<ClientScope> ClientScopes { get; set; } = new();
    public List<AccessToken> AccessTokens { get; set; } = new();
    public List<AuditLog> AuditLogs { get; set; } = new();
}

public class ClientScope
{
    public int Id { get; set; }
    public int ClientId { get; set; }
    public Client Client { get; set; } = null!;
    
    public int ScopeId { get; set; }
    public Scope Scope { get; set; } = null!;
    
    public DateTime GrantedAt { get; set; }
    public string GrantedBy { get; set; } = string.Empty;
}

public class Scope
{
    public int Id { get; set; }
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    
    public ScopeCategory Category { get; set; }
    public bool IsRequired { get; set; }
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; }
    
    // Navigation properties
    public List<ClientScope> ClientScopes { get; set; } = new();
}

public class AccessToken
{
    public int Id { get; set; }
    
    [Required]
    public string TokenId { get; set; } = string.Empty; // JTI claim
    
    public int ClientId { get; set; }
    public Client Client { get; set; } = null!;
    
    public string TokenHash { get; set; } = string.Empty; // SHA256 hash of token
    
    public DateTime IssuedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    
    public bool IsRevoked { get; set; }
    public DateTime? RevokedAt { get; set; }
    public string? RevokedReason { get; set; }
    
    public string IpAddress { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    
    public string Scopes { get; set; } = string.Empty; // JSON array of scopes
}

public class AuditLog
{
    public int Id { get; set; }
    
    public int? ClientId { get; set; }
    public Client? Client { get; set; }
    
    public string Action { get; set; } = string.Empty;
    public string Resource { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    
    public DateTime Timestamp { get; set; }
    public string IpAddress { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}

public enum ClientType
{
    Airline,
    TravelAgency,
    CorporateClient,
    Broker,
    Internal,
    ThirdParty
}

public enum ClientStatus
{
    Active,
    Inactive,
    Suspended,
    PendingApproval,
    Revoked
}

public enum ScopeCategory
{
    Partner,
    Customer,
    Order,
    Finance,
    Product,
    Trip,
    Document,
    Registry,
    Admin
}

// DTOs for API
public class CreateClientRequest
{
    [Required]
    public string ClientName { get; set; } = string.Empty;
    
    public string Description { get; set; } = string.Empty;
    
    [Required]
    public ClientType ClientType { get; set; }
    
    public int AccessTokenLifetimeSeconds { get; set; } = 3600;
    public int RateLimitPerHour { get; set; } = 1000;
    
    public string? AllowedIpAddresses { get; set; }
    public string? WebhookUrl { get; set; }
    
    [Required]
    public List<string> Scopes { get; set; } = new();
}

public class ClientResponse
{
    public int Id { get; set; }
    public string ClientId { get; set; } = string.Empty;
    public string ClientName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ClientType ClientType { get; set; }
    public ClientStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public int AccessTokenLifetimeSeconds { get; set; }
    public int RateLimitPerHour { get; set; }
    public List<string> Scopes { get; set; } = new();
}

public class CreateClientResponse
{
    public int Id { get; set; }
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty; // Plain text - only returned once
    public string ClientName { get; set; } = string.Empty;
    public ClientStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<string> Scopes { get; set; } = new();
}

public class TokenRequest
{
    [Required]
    public string GrantType { get; set; } = string.Empty;
    
    [Required]
    public string ClientId { get; set; } = string.Empty;
    
    [Required]
    public string ClientSecret { get; set; } = string.Empty;
    
    public string? Scope { get; set; }
}

public class TokenResponse
{
    public string AccessToken { get; set; } = string.Empty;
    public string TokenType { get; set; } = "Bearer";
    public int ExpiresIn { get; set; }
    public string Scope { get; set; } = string.Empty;
    public string TokenId { get; set; } = string.Empty;
}

public class TokenErrorResponse
{
    public string Error { get; set; } = string.Empty;
    public string ErrorDescription { get; set; } = string.Empty;
    public string? ErrorUri { get; set; }
}
