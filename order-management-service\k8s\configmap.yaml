apiVersion: v1
kind: ConfigMap
metadata:
  name: order-management-config
  namespace: aviation-system
  labels:
    app: order-management-api
data:
  appsettings.Production.json: |
    {
      "Logging": {
        "LogLevel": {
          "Default": "Information",
          "Microsoft.AspNetCore": "Warning",
          "Microsoft.EntityFrameworkCore": "Warning"
        }
      },
      "AllowedHosts": "*",
      "OAuth2": {
        "RequireHttpsMetadata": true,
        "TokenValidation": {
          "ValidateLifetime": true,
          "ValidateIssuer": true,
          "ValidateAudience": true,
          "ValidateIssuerSigningKey": true,
          "ClockSkewMinutes": 2
        }
      }
    }
---
apiVersion: v1
kind: Secret
metadata:
  name: order-management-secrets
  namespace: aviation-system
  labels:
    app: order-management-api
type: Opaque
stringData:
  database-connection-string: "Server=sql-server-service;Database=AviationOrderManagement;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;"
