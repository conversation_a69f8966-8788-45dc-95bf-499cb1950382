using Aviation.Authentication.Api.Models;

namespace Aviation.Authentication.Api.Services;

public interface IAzureAdService
{
    Task<AzureAdUser?> ValidateTokenAsync(string accessToken);
    Task<AzureAdUser?> GetUserByObjectIdAsync(Guid objectId);
    Task<AzureAdUser?> GetUserByEmailAsync(string email);
    Task<IEnumerable<AzureAdUser>> GetUsersAsync(string? filter = null);
    Task<bool> IsUserInGroupAsync(Guid userId, string groupName);
    Task<IEnumerable<string>> GetUserGroupsAsync(Guid userId);
}

public interface IUserTokenService
{
    Task<string> GenerateUserTokenAsync(User user, List<string> permissions);
    Task<UserTokenValidationResult> ValidateUserTokenAsync(string token);
    Task<bool> RevokeUserTokenAsync(string tokenId);
}

// Azure AD models
public class AzureAdUser
{
    public Guid ObjectId { get; set; }
    public string Email { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string UserPrincipalName { get; set; } = string.Empty;
    public string GivenName { get; set; } = string.Empty;
    public string Surname { get; set; } = string.Empty;
    public string JobTitle { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string OfficeLocation { get; set; } = string.Empty;
    public string EmployeeId { get; set; } = string.Empty;
    public bool AccountEnabled { get; set; }
    public List<string> Groups { get; set; } = new();
}

public class UserTokenValidationResult
{
    public bool IsValid { get; set; }
    public Guid? UserId { get; set; }
    public string? EmployeeId { get; set; }
    public List<string> Permissions { get; set; } = new();
    public List<string> Roles { get; set; } = new();
    public DateTime? ExpiresAt { get; set; }
    public string? ErrorMessage { get; set; }
}

public class AzureAdConfiguration
{
    public string TenantId { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
    public string Instance { get; set; } = "https://login.microsoftonline.com/";
    public string GraphApiUrl { get; set; } = "https://graph.microsoft.com/";
    public List<string> Scopes { get; set; } = new() { "User.Read", "User.ReadBasic.All", "Group.Read.All" };
}

public class UserSyncRequest
{
    public bool SyncAllUsers { get; set; } = false;
    public List<string> SpecificEmails { get; set; } = new();
    public bool UpdateExistingUsers { get; set; } = true;
    public bool CreateMissingUsers { get; set; } = true;
}

public class UserSyncResult
{
    public int TotalProcessed { get; set; }
    public int Created { get; set; }
    public int Updated { get; set; }
    public int Skipped { get; set; }
    public int Errors { get; set; }
    public List<string> ErrorMessages { get; set; } = new();
    public DateTime SyncStarted { get; set; }
    public DateTime SyncCompleted { get; set; }
}
