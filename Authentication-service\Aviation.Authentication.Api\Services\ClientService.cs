using Aviation.Authentication.Api.Data;
using Aviation.Authentication.Api.Models;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace Aviation.Authentication.Api.Services;

public class ClientService : IClientService
{
    private readonly AuthDbContext _context;
    private readonly IAuditService _auditService;
    private readonly ILogger<ClientService> _logger;

    public ClientService(AuthDbContext context, IAuditService auditService, ILogger<ClientService> logger)
    {
        _context = context;
        _auditService = auditService;
        _logger = logger;
    }

    public async Task<Client?> GetClientAsync(string clientId)
    {
        return await _context.Clients
            .Include(c => c.ClientScopes)
            .ThenInclude(cs => cs.Scope)
            .FirstOrDefaultAsync(c => c.ClientId == clientId);
    }

    public async Task<Client?> GetClientByIdAsync(int id)
    {
        return await _context.Clients
            .Include(c => c.ClientScopes)
            .ThenInclude(cs => cs.Scope)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<IEnumerable<Client>> GetClientsAsync(int page = 1, int pageSize = 20)
    {
        return await _context.Clients
            .Include(c => c.ClientScopes)
            .ThenInclude(cs => cs.Scope)
            .OrderByDescending(c => c.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<CreateClientResponse> CreateClientAsync(CreateClientRequest request, string createdBy)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        
        try
        {
            // Generate client credentials
            var clientId = GenerateClientId(request.ClientName);
            var clientSecret = GenerateClientSecret();
            var hashedSecret = BCrypt.Net.BCrypt.HashPassword(clientSecret);

            // Create client
            var client = new Client
            {
                ClientId = clientId,
                ClientSecret = hashedSecret,
                ClientName = request.ClientName,
                Description = request.Description,
                ClientType = request.ClientType,
                Status = ClientStatus.PendingApproval,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = createdBy,
                AccessTokenLifetimeSeconds = request.AccessTokenLifetimeSeconds,
                RateLimitPerHour = request.RateLimitPerHour,
                AllowedIpAddresses = request.AllowedIpAddresses,
                WebhookUrl = request.WebhookUrl
            };

            _context.Clients.Add(client);
            await _context.SaveChangesAsync();

            // Add scopes
            var scopes = await _context.Scopes
                .Where(s => request.Scopes.Contains(s.Name) && s.IsActive)
                .ToListAsync();

            foreach (var scope in scopes)
            {
                var clientScope = new ClientScope
                {
                    ClientId = client.Id,
                    ScopeId = scope.Id,
                    GrantedAt = DateTime.UtcNow,
                    GrantedBy = createdBy
                };
                _context.ClientScopes.Add(clientScope);
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            // Log client creation
            await _auditService.LogAsync("client_created", "clients", 
                $"Client created: {client.ClientName} ({client.ClientId})", client.Id, "", "", true);

            return new CreateClientResponse
            {
                Id = client.Id,
                ClientId = client.ClientId,
                ClientSecret = clientSecret, // Return plain text secret only once
                ClientName = client.ClientName,
                Status = client.Status,
                CreatedAt = client.CreatedAt,
                Scopes = scopes.Select(s => s.Name).ToList()
            };
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error creating client {ClientName}", request.ClientName);
            throw;
        }
    }

    public async Task<ClientResponse?> UpdateClientAsync(int id, UpdateClientRequest request)
    {
        var client = await _context.Clients
            .Include(c => c.ClientScopes)
            .ThenInclude(cs => cs.Scope)
            .FirstOrDefaultAsync(c => c.Id == id);

        if (client == null)
            return null;

        // Update properties
        if (!string.IsNullOrEmpty(request.ClientName))
            client.ClientName = request.ClientName;
        
        if (!string.IsNullOrEmpty(request.Description))
            client.Description = request.Description;
        
        if (request.ClientType.HasValue)
            client.ClientType = request.ClientType.Value;
        
        if (request.AccessTokenLifetimeSeconds.HasValue)
            client.AccessTokenLifetimeSeconds = request.AccessTokenLifetimeSeconds.Value;
        
        if (request.RateLimitPerHour.HasValue)
            client.RateLimitPerHour = request.RateLimitPerHour.Value;
        
        if (request.AllowedIpAddresses != null)
            client.AllowedIpAddresses = request.AllowedIpAddresses;
        
        if (request.WebhookUrl != null)
            client.WebhookUrl = request.WebhookUrl;

        client.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        // Log client update
        await _auditService.LogAsync("client_updated", "clients", 
            $"Client updated: {client.ClientName} ({client.ClientId})", client.Id, "", "", true);

        return new ClientResponse
        {
            Id = client.Id,
            ClientId = client.ClientId,
            ClientName = client.ClientName,
            Description = client.Description,
            ClientType = client.ClientType,
            Status = client.Status,
            CreatedAt = client.CreatedAt,
            LastUsedAt = client.LastUsedAt,
            AccessTokenLifetimeSeconds = client.AccessTokenLifetimeSeconds,
            RateLimitPerHour = client.RateLimitPerHour,
            Scopes = client.ClientScopes.Select(cs => cs.Scope.Name).ToList()
        };
    }

    public async Task<bool> DeleteClientAsync(int id)
    {
        var client = await _context.Clients.FindAsync(id);
        if (client == null)
            return false;

        // Revoke all active tokens
        var activeTokens = await _context.AccessTokens
            .Where(t => t.ClientId == id && !t.IsRevoked)
            .ToListAsync();

        foreach (var token in activeTokens)
        {
            token.IsRevoked = true;
            token.RevokedAt = DateTime.UtcNow;
            token.RevokedReason = "Client deleted";
        }

        _context.Clients.Remove(client);
        await _context.SaveChangesAsync();

        // Log client deletion
        await _auditService.LogAsync("client_deleted", "clients", 
            $"Client deleted: {client.ClientName} ({client.ClientId})", client.Id, "", "", true);

        return true;
    }

    public async Task<bool> ValidateClientCredentialsAsync(string clientId, string clientSecret)
    {
        var client = await _context.Clients
            .FirstOrDefaultAsync(c => c.ClientId == clientId && c.Status == ClientStatus.Active);

        if (client == null)
            return false;

        return BCrypt.Net.BCrypt.Verify(clientSecret, client.ClientSecret);
    }

    public async Task<IEnumerable<string>> GetClientScopesAsync(string clientId)
    {
        var client = await _context.Clients
            .Include(c => c.ClientScopes)
            .ThenInclude(cs => cs.Scope)
            .FirstOrDefaultAsync(c => c.ClientId == clientId);

        return client?.ClientScopes
            .Where(cs => cs.Scope.IsActive)
            .Select(cs => cs.Scope.Name) ?? Enumerable.Empty<string>();
    }

    public async Task<bool> UpdateClientScopesAsync(int clientId, List<string> scopes, string updatedBy)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        
        try
        {
            var client = await _context.Clients
                .Include(c => c.ClientScopes)
                .FirstOrDefaultAsync(c => c.Id == clientId);

            if (client == null)
                return false;

            // Remove existing scopes
            _context.ClientScopes.RemoveRange(client.ClientScopes);

            // Add new scopes
            var validScopes = await _context.Scopes
                .Where(s => scopes.Contains(s.Name) && s.IsActive)
                .ToListAsync();

            foreach (var scope in validScopes)
            {
                var clientScope = new ClientScope
                {
                    ClientId = clientId,
                    ScopeId = scope.Id,
                    GrantedAt = DateTime.UtcNow,
                    GrantedBy = updatedBy
                };
                _context.ClientScopes.Add(clientScope);
            }

            client.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            // Log scope update
            await _auditService.LogAsync("client_scopes_updated", "clients", 
                $"Scopes updated for client: {client.ClientName}, new scopes: {string.Join(", ", scopes)}", 
                client.Id, "", "", true);

            return true;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error updating scopes for client {ClientId}", clientId);
            return false;
        }
    }

    public async Task<bool> UpdateClientStatusAsync(int clientId, ClientStatus status, string updatedBy)
    {
        var client = await _context.Clients.FindAsync(clientId);
        if (client == null)
            return false;

        var oldStatus = client.Status;
        client.Status = status;
        client.UpdatedAt = DateTime.UtcNow;

        // If suspending or revoking, revoke all active tokens
        if (status == ClientStatus.Suspended || status == ClientStatus.Revoked)
        {
            var activeTokens = await _context.AccessTokens
                .Where(t => t.ClientId == clientId && !t.IsRevoked)
                .ToListAsync();

            foreach (var token in activeTokens)
            {
                token.IsRevoked = true;
                token.RevokedAt = DateTime.UtcNow;
                token.RevokedReason = $"Client status changed to {status}";
            }
        }

        await _context.SaveChangesAsync();

        // Log status change
        await _auditService.LogAsync("client_status_changed", "clients", 
            $"Client status changed from {oldStatus} to {status}: {client.ClientName}", 
            client.Id, "", "", true);

        return true;
    }

    public async Task UpdateLastUsedAsync(string clientId)
    {
        var client = await _context.Clients.FirstOrDefaultAsync(c => c.ClientId == clientId);
        if (client != null)
        {
            client.LastUsedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }

    private static string GenerateClientId(string clientName)
    {
        var prefix = string.Concat(clientName.Where(char.IsLetterOrDigit)).ToLower();
        if (prefix.Length > 20)
            prefix = prefix.Substring(0, 20);
        
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
        return $"{prefix}_{timestamp}";
    }

    private static string GenerateClientSecret()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }
}
