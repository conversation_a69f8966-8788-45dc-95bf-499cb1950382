2025-07-31 12:16:51.217 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-31 12:16:51.360 +05:30 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5293: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.
 ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-07-31 12:23:36.142 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-07-31 12:23:36.238 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 12:23:36.246 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 12:23:36.251 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 12:23:36.254 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 12:23:46.974 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users - application/json 244 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:47.020 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:47.272 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:47.301 +05:30 INF] Route matched with {action = "CreateUser", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserResponse]] CreateUser(Aviation.Authentication.Api.Models.CreateUserRequest) on controller Aviation.Authentication.Api.Controllers.UsersController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d782b4d2-91c8-42a9-ab92-5c44a02a5208","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.011 +05:30 INF] Executed DbCommand (73ms) [Parameters=[@__request_Email_0='?' (Size = 255), @__request_EmployeeId_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
FROM [Users] AS [u]
WHERE [u].[Email] = @__request_Email_0 OR [u].[EmployeeId] = @__request_EmployeeId_1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d782b4d2-91c8-42a9-ab92-5c44a02a5208","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.241 +05:30 INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Guid), @p2='?' (DbType = DateTime2), @p3='?' (Size = 255), @p4='?' (Size = 20), @p5='?' (DbType = Int32), @p6='?' (Size = 100), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime2), @p9='?' (Size = 100), @p10='?' (DbType = DateTime2), @p11='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([UserId], [AzureAdObjectId], [CreatedDate], [Email], [EmployeeId], [FailedAttempts], [FirstName], [IsActive], [LastLogin], [LastName], [LockoutEnd], [ModifiedDate])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d782b4d2-91c8-42a9-ab92-5c44a02a5208","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.357 +05:30 INF] Executed DbCommand (23ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[RoleId], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
FROM [Roles] AS [r]
WHERE [r].[RoleId] = @__get_Item_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d782b4d2-91c8-42a9-ab92-5c44a02a5208","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.586 +05:30 INF] Executed DbCommand (10ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Guid), @p3='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [UserRoles] ([UserRoleId], [AssignedDate], [RoleId], [UserId])
VALUES (@p0, @p1, @p2, @p3); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d782b4d2-91c8-42a9-ab92-5c44a02a5208","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.674 +05:30 INF] Executed DbCommand (8ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d782b4d2-91c8-42a9-ab92-5c44a02a5208","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.745 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@__user_UserId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[RoleId], [r].[RoleCode], [r].[Name], [r].[Description], [r].[IsActive], [u].[AssignedDate]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[RoleId]
WHERE [u].[UserId] = @__user_UserId_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d782b4d2-91c8-42a9-ab92-5c44a02a5208","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.771 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"d782b4d2-91c8-42a9-ab92-5c44a02a5208","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.790 +05:30 INF] Executing CreatedAtActionResult, writing value of type 'Aviation.Authentication.Api.Models.UserResponse'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d782b4d2-91c8-42a9-ab92-5c44a02a5208","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.850 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api) in 3536.049ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.859 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:23:50.879 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users - 201 null application/json; charset=utf-8 3905.0738ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUD:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUD"}
[2025-07-31 12:27:57.981 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/users?page=1&pageSize=20 - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUF:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUF"}
[2025-07-31 12:27:58.017 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.UsersController.GetUsers (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUF:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUF"}
[2025-07-31 12:27:58.034 +05:30 INF] Route matched with {action = "GetUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[Aviation.Authentication.Api.Models.UserResponse]]] GetUsers(Int32, Int32) on controller Aviation.Authentication.Api.Controllers.UsersController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"842903a8-ed20-4f99-828e-79fdbab79927","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.GetUsers (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUF:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUF"}
[2025-07-31 12:27:58.526 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    ORDER BY [u].[LastName], [u].[FirstName]
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[LastName], [t].[FirstName], [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"842903a8-ed20-4f99-828e-79fdbab79927","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.GetUsers (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUF:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUF"}
[2025-07-31 12:27:58.550 +05:30 INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+SelectListIterator`2[[Aviation.Authentication.Api.Models.User, Aviation.Authentication.Api, Version=*******, Culture=neutral, PublicKeyToken=null],[Aviation.Authentication.Api.Models.UserResponse, Aviation.Authentication.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"842903a8-ed20-4f99-828e-79fdbab79927","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.GetUsers (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUF:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUF"}
[2025-07-31 12:27:58.560 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.UsersController.GetUsers (Aviation.Authentication.Api) in 517.0498ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUF:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUF"}
[2025-07-31 12:27:58.568 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.UsersController.GetUsers (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUF:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUF"}
[2025-07-31 12:27:58.574 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/users?page=1&pageSize=20 - 200 null application/json; charset=utf-8 593.412ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUF:********","RequestPath":"/api/users","ConnectionId":"0HNEFVRVLSOUF"}
[2025-07-31 12:37:31.217 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/config - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:31.290 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:31.331 +05:30 INF] Route matched with {action = "GetConfig", controller = "AzureAdTest"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetConfig() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"2bd3bd2e-e597-419a-9bf5-ce03a0a657eb","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:31.399 +05:30 INF] Getting Azure AD configuration... {"SourceContext":"Aviation.Authentication.Api.Controllers.AzureAdTestController","ActionId":"2bd3bd2e-e597-419a-9bf5-ce03a0a657eb","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:31.430 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`7[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"2bd3bd2e-e597-419a-9bf5-ce03a0a657eb","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:31.525 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api) in 156.3747ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:31.711 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:31.746 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/config - 200 null application/json; charset=utf-8 529.9917ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:41.117 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-service - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:41.161 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:41.201 +05:30 INF] Route matched with {action = "TestService", controller = "AzureAdTest"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult TestService() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"e99e1f3c-5691-486e-91e8-567b9ce3c968","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:41.260 +05:30 INF] Testing Azure AD service instantiation... {"SourceContext":"Aviation.Authentication.Api.Controllers.AzureAdTestController","ActionId":"e99e1f3c-5691-486e-91e8-567b9ce3c968","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:41.280 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType4`4[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"e99e1f3c-5691-486e-91e8-567b9ce3c968","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:41.308 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api) in 50.17ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:41.322 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:41.345 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-service - 200 null application/json; charset=utf-8 227.896ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:47.796 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-connection - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:47.865 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:47.912 +05:30 INF] Route matched with {action = "TestConnection", controller = "AzureAdTest"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] TestConnection() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"0774aca8-1b97-4b40-8c3f-e6d85fa95d8c","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:48.050 +05:30 INF] Testing Azure AD connection... {"SourceContext":"Aviation.Authentication.Api.Controllers.AzureAdTestController","ActionId":"0774aca8-1b97-4b40-8c3f-e6d85fa95d8c","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:48.201 +05:30 INF] Getting Azure AD users with filter: none {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"0774aca8-1b97-4b40-8c3f-e6d85fa95d8c","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:51.676 +05:30 ERR] Error getting Azure AD users {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"0774aca8-1b97-4b40-8c3f-e6d85fa95d8c","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEFVRVLSOUH"}
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUsersAsync(String filter) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 154
[2025-07-31 12:37:51.775 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType5`4[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"0774aca8-1b97-4b40-8c3f-e6d85fa95d8c","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:51.828 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api) in 3889.6819ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:51.859 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 12:37:51.881 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-connection - 200 null application/json; charset=utf-8 4084.5603ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUH:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEFVRVLSOUH"}
[2025-07-31 14:14:05.963 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 93 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUJ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUJ"}
[2025-07-31 14:14:07.517 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUJ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUJ"}
[2025-07-31 14:14:07.696 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUJ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUJ"}
[2025-07-31 14:14:07.932 +05:30 INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUJ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUJ"}
[2025-07-31 14:14:08.017 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 282.5895ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUJ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUJ"}
[2025-07-31 14:14:08.027 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUJ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUJ"}
[2025-07-31 14:14:08.066 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 400 null application/problem+json; charset=utf-8 2106.9561ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUJ:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUJ"}
[2025-07-31 14:42:31.171 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users/sync-azure-ad - application/json 162 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUL:********","RequestPath":"/api/users/sync-azure-ad","ConnectionId":"0HNEFVRVLSOUL"}
[2025-07-31 14:42:31.196 +05:30 INF] Executing endpoint '405 HTTP Method Not Supported' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUL:********","RequestPath":"/api/users/sync-azure-ad","ConnectionId":"0HNEFVRVLSOUL"}
[2025-07-31 14:42:31.207 +05:30 INF] Executed endpoint '405 HTTP Method Not Supported' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUL:********","RequestPath":"/api/users/sync-azure-ad","ConnectionId":"0HNEFVRVLSOUL"}
[2025-07-31 14:42:31.213 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users/sync-azure-ad - 405 0 null 49.0098ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUL:********","RequestPath":"/api/users/sync-azure-ad","ConnectionId":"0HNEFVRVLSOUL"}
[2025-07-31 14:45:25.184 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api//users/sync-from-azure-ad - application/json 162 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api//users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:45:25.586 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api//users/sync-from-azure-ad - 404 0 null 402.4812ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api//users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:45:25.628 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://localhost:5293/api//users/sync-from-azure-ad, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api//users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:45:38.124 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - application/json 162 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:45:38.171 +05:30 INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user. {"EventId":{"Id":2,"Name":"UserAuthorizationFailed"},"SourceContext":"Microsoft.AspNetCore.Authorization.DefaultAuthorizationService","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:45:38.310 +05:30 INF] AuthenticationScheme: Bearer was challenged. {"EventId":{"Id":12,"Name":"AuthenticationSchemeChallenged"},"SourceContext":"Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:45:38.319 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - 401 0 null 195.1063ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:05.225 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - application/json 162 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:05.652 +05:30 INF] Failed to validate the token. {"EventId":{"Id":1,"Name":"TokenValidationFailed"},"SourceContext":"Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
System.ArgumentException: IDX14102: Unable to decode the header '[PII of type 'Microsoft.IdentityModel.Logging.SecurityArtifact' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]' as Base64Url encoded string.
 ---> System.FormatException: IDX10400: Unable to decode: '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]' as Base64url encoded string.
   at Microsoft.IdentityModel.Tokens.Base64UrlEncoding.ValidateAndGetOutputSize(ReadOnlySpan`1 strSpan, Int32 offset, Int32 length)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebToken.CreateClaimSet(ReadOnlySpan`1 strSpan, Int32 startIndex, Int32 length, Boolean createHeaderClaimSet)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebToken.ReadToken(ReadOnlyMemory`1 encodedTokenMemory)
   --- End of inner exception stack trace ---
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebToken.ReadToken(ReadOnlyMemory`1 encodedTokenMemory)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebToken..ctor(String jwtEncodedString)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ReadToken(String token, TokenValidationParameters validationParameters)
[2025-07-31 14:46:05.718 +05:30 INF] Bearer was not authenticated. Failure message: IDX14102: Unable to decode the header '[PII of type 'Microsoft.IdentityModel.Logging.SecurityArtifact' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]' as Base64Url encoded string. {"EventId":{"Id":7,"Name":"AuthenticationSchemeNotAuthenticatedWithFailure"},"SourceContext":"Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:05.736 +05:30 INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user. {"EventId":{"Id":2,"Name":"UserAuthorizationFailed"},"SourceContext":"Microsoft.AspNetCore.Authorization.DefaultAuthorizationService","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:05.741 +05:30 INF] AuthenticationScheme: Bearer was challenged. {"EventId":{"Id":12,"Name":"AuthenticationSchemeChallenged"},"SourceContext":"Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:05.749 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - 401 0 null 523.8197ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:15.026 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 89 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:15.039 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:15.055 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:15.809 +05:30 INF] Executed DbCommand (45ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:15.920 +05:30 INF] Executed DbCommand (19ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name] AS [Permission], [e].[Name] AS [Entity], [m].[Name] AS [Module], [s].[Name] AS [SubModule]
FROM [UserRoles] AS [u]
INNER JOIN [RolePermissions] AS [r] ON [u].[RoleId] = [r].[RoleId]
INNER JOIN [Permissions] AS [p] ON [r].[PermissionId] = [p].[PermissionId]
LEFT JOIN [Entities] AS [e] ON [r].[EntityId] = [e].[EntityId]
LEFT JOIN [Modules] AS [m] ON [r].[ModuleId] = [m].[ModuleId]
LEFT JOIN [SubModules] AS [s] ON [r].[SubModuleId] = [s].[SubModuleId]
WHERE [u].[UserId] = @__userId_0 AND [r].[Granted] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:16.098 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Boolean), @p4='?' (DbType = DateTime2), @p5='?' (DbType = DateTime2), @p6='?' (Size = 500), @p7='?' (Size = 1000), @p8='?' (Size = 500), @p9='?' (Size = 100), @p10='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AccessTokens] ([ClientId], [ExpiresAt], [IpAddress], [IsRevoked], [IssuedAt], [RevokedAt], [RevokedReason], [Scopes], [TokenHash], [TokenId], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:16.138 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[UserId] = @__userId_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:16.179 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [LastLogin] = @p0, [ModifiedDate] = @p1
OUTPUT 1
WHERE [UserId] = @p2; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:16.202 +05:30 INF] Executed DbCommand (10ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:16.225 +05:30 INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[RoleId], [r].[RoleCode], [r].[Name], [r].[Description], [r].[IsActive], [u].[AssignedDate]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[RoleId]
WHERE [u].[UserId] = @__userId_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:16.280 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:16.291 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Models.UserLoginResponse'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"57fb5a3e-f216-4ee4-9280-efa08eeaa2c4","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:16.307 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 1244.4114ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:16.314 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:16.321 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 200 null application/json; charset=utf-8 1294.7379ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:28.138 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - application/json 162 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:28.154 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:28.167 +05:30 INF] Route matched with {action = "SyncFromAzureAd", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Services.UserSyncResult]] SyncFromAzureAd(Aviation.Authentication.Api.Services.UserSyncRequest) on controller Aviation.Authentication.Api.Controllers.UsersController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:28.378 +05:30 INF] Getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:29.215 +05:30 ERR] Error getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUserByEmailAsync(String email) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 114
[2025-07-31 14:46:29.316 +05:30 INF] Getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:29.577 +05:30 ERR] Error getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUserByEmailAsync(String email) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 114
[2025-07-31 14:46:29.622 +05:30 INF] Executed DbCommand (14ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:29.634 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Services.UserSyncResult'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:29.642 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api) in 1468.5029ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:29.650 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:46:29.658 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - 200 null application/json; charset=utf-8 1520.2179ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUN:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUN"}
[2025-07-31 14:47:28.230 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - application/json 170 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:47:28.242 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:47:28.246 +05:30 INF] Route matched with {action = "SyncFromAzureAd", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Services.UserSyncResult]] SyncFromAzureAd(Aviation.Authentication.Api.Services.UserSyncRequest) on controller Aviation.Authentication.Api.Controllers.UsersController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:47:28.256 +05:30 INF] Getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:47:29.045 +05:30 ERR] Error getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUserByEmailAsync(String email) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 114
[2025-07-31 14:47:29.058 +05:30 INF] Getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:47:29.224 +05:30 ERR] Error getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUserByEmailAsync(String email) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 114
[2025-07-31 14:47:29.261 +05:30 INF] Executed DbCommand (17ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:47:29.273 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Services.UserSyncResult'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:47:29.287 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api) in 1033.2286ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:47:29.294 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:47:29.304 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - 200 null application/json; charset=utf-8 1073.9761ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:04.220 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - application/json 174 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:04.232 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:04.238 +05:30 INF] Route matched with {action = "SyncFromAzureAd", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Services.UserSyncResult]] SyncFromAzureAd(Aviation.Authentication.Api.Services.UserSyncRequest) on controller Aviation.Authentication.Api.Controllers.UsersController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:04.247 +05:30 INF] Getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:05.804 +05:30 ERR] Error getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUserByEmailAsync(String email) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 114
[2025-07-31 14:48:05.825 +05:30 INF] Getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:05.991 +05:30 ERR] Error getting Azure AD user by email: <EMAIL> {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUserByEmailAsync(String email) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 114
[2025-07-31 14:48:06.043 +05:30 INF] Executed DbCommand (19ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:06.063 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Services.UserSyncResult'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:06.075 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api) in 1828.9824ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:06.083 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:06.095 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - 200 null application/json; charset=utf-8 1874.7857ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:18.420 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - application/json 93 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:18.445 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:18.462 +05:30 INF] Route matched with {action = "SyncFromAzureAd", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Services.UserSyncResult]] SyncFromAzureAd(Aviation.Authentication.Api.Services.UserSyncRequest) on controller Aviation.Authentication.Api.Controllers.UsersController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:18.476 +05:30 INF] Getting Azure AD users with filter: accountEnabled eq true {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:18.944 +05:30 ERR] Error getting Azure AD users {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUsersAsync(String filter) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 154
[2025-07-31 14:48:18.973 +05:30 INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:18.986 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Services.UserSyncResult'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"84a7a764-991f-4df9-b1ed-ea14f49b0b88","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:18.994 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api) in 519.3702ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:19.000 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
[2025-07-31 14:48:19.008 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - 200 null application/json; charset=utf-8 587.7705ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFVRVLSOUP:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEFVRVLSOUP"}
