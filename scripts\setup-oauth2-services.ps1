# PowerShell script to set up OAuth 2.0 for Aviation Management System B2B services

param(
    [Parameter(Mandatory=$true)]
    [string]$Environment = "Development",
    
    [Parameter(Mandatory=$false)]
    [string]$AuthorityUrl = "https://your-auth-server.com",
    
    [Parameter(Mandatory=$false)]
    [string]$SigningKey = "your-super-secret-signing-key-here-minimum-256-bits"
)

Write-Host "Setting up OAuth 2.0 for Aviation Management System B2B Services" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow

# Define service directories
$services = @(
    "api-gateway/Aviation.ApiGateway",
    "partner-customer-service/Aviation.PartnerCustomer.Api",
    "order-management-service/Aviation.OrderManagement.Api",
    "finance-billing-service/Aviation.FinanceBilling.Api",
    "product-pricing-service/Aviation.ProductPricing.Api",
    "trip-estimation-service/Aviation.TripEstimation.Api",
    "document-service/Aviation.Document.Api",
    "devportal-appregistry-service/Aviation.AppRegistry.Api"
)

# OAuth 2.0 configuration template
$oauth2Config = @{
    OAuth2 = @{
        Authority = $AuthorityUrl
        Audience = "aviation-api"
        Issuer = $AuthorityUrl
        SigningKey = $SigningKey
        RequireHttpsMetadata = if ($Environment -eq "Development") { $false } else { $true }
        TokenValidation = @{
            ValidateLifetime = $true
            ValidateIssuer = $true
            ValidateAudience = $true
            ValidateIssuerSigningKey = $true
            ClockSkewMinutes = if ($Environment -eq "Development") { 5 } else { 2 }
        }
    }
}

# Function to update appsettings.json
function Update-AppSettings {
    param(
        [string]$ServicePath,
        [hashtable]$Config
    )
    
    $appSettingsPath = Join-Path $ServicePath "appsettings.json"
    
    if (Test-Path $appSettingsPath) {
        Write-Host "Updating $appSettingsPath" -ForegroundColor Cyan
        
        # Read existing configuration
        $existingConfig = Get-Content $appSettingsPath | ConvertFrom-Json -AsHashtable
        
        # Merge OAuth2 configuration
        $existingConfig.OAuth2 = $Config.OAuth2
        
        # Write back to file
        $existingConfig | ConvertTo-Json -Depth 10 | Set-Content $appSettingsPath
        
        Write-Host "✓ Updated $appSettingsPath" -ForegroundColor Green
    } else {
        Write-Warning "appsettings.json not found at $appSettingsPath"
    }
}

# Function to create environment-specific appsettings
function Create-EnvironmentSettings {
    param(
        [string]$ServicePath,
        [string]$Environment,
        [hashtable]$Config
    )
    
    $envSettingsPath = Join-Path $ServicePath "appsettings.$Environment.json"
    
    Write-Host "Creating $envSettingsPath" -ForegroundColor Cyan
    
    $envConfig = @{
        OAuth2 = $Config.OAuth2
        Logging = @{
            LogLevel = @{
                Default = if ($Environment -eq "Development") { "Debug" } else { "Information" }
                "Microsoft.AspNetCore" = "Warning"
                "Aviation.Auth.OAuth2" = if ($Environment -eq "Development") { "Debug" } else { "Information" }
            }
        }
    }
    
    $envConfig | ConvertTo-Json -Depth 10 | Set-Content $envSettingsPath
    Write-Host "✓ Created $envSettingsPath" -ForegroundColor Green
}

# Function to add OAuth 2.0 package references
function Add-OAuth2Packages {
    param(
        [string]$ServicePath
    )
    
    $projectFile = Get-ChildItem -Path $ServicePath -Filter "*.csproj" | Select-Object -First 1
    
    if ($projectFile) {
        Write-Host "Adding OAuth 2.0 package reference to $($projectFile.Name)" -ForegroundColor Cyan
        
        Push-Location $ServicePath
        
        # Add reference to shared OAuth 2.0 library
        dotnet add reference "..\..\Shared\Aviation.Auth.OAuth2\Aviation.Auth.OAuth2.csproj"
        
        Pop-Location
        
        Write-Host "✓ Added OAuth 2.0 package reference" -ForegroundColor Green
    }
}

# Main execution
Write-Host "`nProcessing services..." -ForegroundColor Yellow

foreach ($service in $services) {
    Write-Host "`n--- Processing $service ---" -ForegroundColor Magenta
    
    if (Test-Path $service) {
        # Update appsettings.json
        Update-AppSettings -ServicePath $service -Config $oauth2Config
        
        # Create environment-specific settings
        Create-EnvironmentSettings -ServicePath $service -Environment $Environment -Config $oauth2Config
        
        # Add OAuth 2.0 package references
        Add-OAuth2Packages -ServicePath $service
    } else {
        Write-Warning "Service directory not found: $service"
    }
}

# Create global configuration file
Write-Host "`n--- Creating global OAuth 2.0 configuration ---" -ForegroundColor Magenta

$globalConfigPath = "config/oauth2-global.json"
$globalConfigDir = Split-Path $globalConfigPath -Parent

if (!(Test-Path $globalConfigDir)) {
    New-Item -ItemType Directory -Path $globalConfigDir -Force
}

$globalConfig = @{
    OAuth2 = $oauth2Config.OAuth2
    Services = @{
        ApiGateway = @{
            Port = 5000
            Url = "https://api.aviation-management.com"
        }
        PartnerCustomerService = @{
            Port = 5001
            Url = "https://partner-api.aviation-management.com"
        }
        OrderManagementService = @{
            Port = 5002
            Url = "https://order-api.aviation-management.com"
        }
        FinanceBillingService = @{
            Port = 5003
            Url = "https://billing-api.aviation-management.com"
        }
        ProductPricingService = @{
            Port = 5004
            Url = "https://product-api.aviation-management.com"
        }
        TripEstimationService = @{
            Port = 5005
            Url = "https://trip-api.aviation-management.com"
        }
        DocumentService = @{
            Port = 5006
            Url = "https://document-api.aviation-management.com"
        }
        AppRegistryService = @{
            Port = 5007
            Url = "https://registry-api.aviation-management.com"
        }
    }
    B2BScopes = @(
        "partner:read", "partner:write",
        "customer:read", "customer:write",
        "order:read", "order:write", "order:cancel",
        "invoice:read", "invoice:write", "billing:read",
        "product:read", "product:write",
        "pricing:read", "pricing:write",
        "trip:estimate", "flight:read", "airport:read",
        "document:read", "document:write", "document:delete",
        "app:read", "app:write", "app:admin"
    )
}

$globalConfig | ConvertTo-Json -Depth 10 | Set-Content $globalConfigPath
Write-Host "✓ Created global configuration: $globalConfigPath" -ForegroundColor Green

Write-Host "`n🎉 OAuth 2.0 setup completed successfully!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Update the Authority URL and Signing Key in configuration files" -ForegroundColor White
Write-Host "2. Set up your OAuth 2.0 authorization server" -ForegroundColor White
Write-Host "3. Register B2B client applications" -ForegroundColor White
Write-Host "4. Test the authentication flow" -ForegroundColor White
Write-Host "5. Deploy services to your target environment" -ForegroundColor White
