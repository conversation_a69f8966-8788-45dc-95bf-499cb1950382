using Aviation.Auth.OAuth2.Authorization;
using Aviation.Auth.OAuth2.Models;
using Aviation.OrderManagement.Api.Data;
using Aviation.OrderManagement.Api.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Aviation.OrderManagement.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class OrdersController : ControllerBase
{
    private readonly OrderDbContext _context;
    private readonly ILogger<OrdersController> _logger;

    public OrdersController(OrderDbContext context, ILogger<OrdersController> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Get all orders with optional filtering
    /// </summary>
    [HttpGet]
    [RequireScope(B2BScopes.OrderRead)]
    public async Task<ActionResult<IEnumerable<Order>>> GetOrders(
        [FromQuery] int? partnerId = null,
        [FromQuery] int? customerId = null,
        [FromQuery] OrderStatus? status = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        _logger.LogInformation("Getting orders - PartnerId: {PartnerId}, CustomerId: {CustomerId}, Status: {Status}", 
            partnerId, customerId, status);

        var query = _context.Orders
            .Include(o => o.OrderItems)
            .Include(o => o.Passengers)
            .AsQueryable();

        if (partnerId.HasValue)
            query = query.Where(o => o.PartnerId == partnerId.Value);

        if (customerId.HasValue)
            query = query.Where(o => o.CustomerId == customerId.Value);

        if (status.HasValue)
            query = query.Where(o => o.Status == status.Value);

        var totalCount = await query.CountAsync();
        var orders = await query
            .OrderByDescending(o => o.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        Response.Headers.Add("X-Total-Count", totalCount.ToString());
        Response.Headers.Add("X-Page", page.ToString());
        Response.Headers.Add("X-Page-Size", pageSize.ToString());

        return Ok(orders);
    }

    /// <summary>
    /// Get order by ID
    /// </summary>
    [HttpGet("{id}")]
    [RequireScope(B2BScopes.OrderRead)]
    public async Task<ActionResult<Order>> GetOrder(int id)
    {
        _logger.LogInformation("Getting order with ID: {OrderId}", id);

        var order = await _context.Orders
            .Include(o => o.OrderItems)
            .Include(o => o.Passengers)
            .FirstOrDefaultAsync(o => o.Id == id);

        if (order == null)
        {
            return NotFound($"Order with ID {id} not found");
        }

        return Ok(order);
    }

    /// <summary>
    /// Create a new order
    /// </summary>
    [HttpPost]
    [RequireScope(B2BScopes.OrderWrite)]
    public async Task<ActionResult<Order>> CreateOrder([FromBody] CreateOrderRequest request)
    {
        _logger.LogInformation("Creating new order for Customer: {CustomerId}, Partner: {PartnerId}", 
            request.CustomerId, request.PartnerId);

        var order = new Order
        {
            OrderNumber = GenerateOrderNumber(),
            CustomerId = request.CustomerId,
            PartnerId = request.PartnerId,
            DepartureAirport = request.DepartureAirport,
            ArrivalAirport = request.ArrivalAirport,
            DepartureDate = request.DepartureDate,
            ReturnDate = request.ReturnDate,
            PassengerCount = request.PassengerCount,
            FlightType = request.FlightType,
            Status = OrderStatus.Draft,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedBy = User.Identity?.Name ?? "api-client"
        };

        // Add order items
        foreach (var itemRequest in request.OrderItems)
        {
            var orderItem = new OrderItem
            {
                ItemType = itemRequest.ItemType,
                ItemDescription = itemRequest.ItemDescription,
                UnitPrice = itemRequest.UnitPrice,
                Quantity = itemRequest.Quantity,
                TotalPrice = itemRequest.UnitPrice * itemRequest.Quantity,
                ExternalReference = itemRequest.ExternalReference,
                ServiceDate = itemRequest.ServiceDate
            };
            order.OrderItems.Add(orderItem);
        }

        // Add passengers
        foreach (var passengerRequest in request.Passengers)
        {
            var passenger = new Passenger
            {
                FirstName = passengerRequest.FirstName,
                LastName = passengerRequest.LastName,
                DateOfBirth = passengerRequest.DateOfBirth,
                PassportNumber = passengerRequest.PassportNumber,
                Nationality = passengerRequest.Nationality,
                Type = passengerRequest.Type
            };
            order.Passengers.Add(passenger);
        }

        // Calculate total amount
        order.TotalAmount = order.OrderItems.Sum(item => item.TotalPrice);

        _context.Orders.Add(order);
        await _context.SaveChangesAsync();

        return CreatedAtAction(nameof(GetOrder), new { id = order.Id }, order);
    }

    /// <summary>
    /// Update order status
    /// </summary>
    [HttpPatch("{id}/status")]
    [RequireScope(B2BScopes.OrderWrite)]
    public async Task<ActionResult<Order>> UpdateOrderStatus(int id, [FromBody] UpdateOrderStatusRequest request)
    {
        _logger.LogInformation("Updating order {OrderId} status to {Status}", id, request.Status);

        var order = await _context.Orders.FindAsync(id);
        if (order == null)
        {
            return NotFound($"Order with ID {id} not found");
        }

        // Validate status transition
        if (!IsValidStatusTransition(order.Status, request.Status))
        {
            return BadRequest($"Invalid status transition from {order.Status} to {request.Status}");
        }

        order.Status = request.Status;
        order.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return Ok(order);
    }

    /// <summary>
    /// Cancel an order
    /// </summary>
    [HttpPost("{id}/cancel")]
    [RequireScope(B2BScopes.OrderCancel)]
    public async Task<ActionResult<Order>> CancelOrder(int id, [FromBody] string? reason = null)
    {
        _logger.LogInformation("Cancelling order {OrderId} with reason: {Reason}", id, reason);

        var order = await _context.Orders.FindAsync(id);
        if (order == null)
        {
            return NotFound($"Order with ID {id} not found");
        }

        if (order.Status == OrderStatus.Completed || order.Status == OrderStatus.Cancelled)
        {
            return BadRequest($"Cannot cancel order with status {order.Status}");
        }

        order.Status = OrderStatus.Cancelled;
        order.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return Ok(order);
    }

    /// <summary>
    /// Delete an order (soft delete by setting status to cancelled)
    /// </summary>
    [HttpDelete("{id}")]
    [RequireScope(B2BScopes.OrderWrite)]
    public async Task<ActionResult> DeleteOrder(int id)
    {
        _logger.LogInformation("Deleting order with ID: {OrderId}", id);

        var order = await _context.Orders.FindAsync(id);
        if (order == null)
        {
            return NotFound($"Order with ID {id} not found");
        }

        if (order.Status == OrderStatus.Confirmed || order.Status == OrderStatus.InProgress)
        {
            return BadRequest("Cannot delete confirmed or in-progress orders. Cancel them instead.");
        }

        _context.Orders.Remove(order);
        await _context.SaveChangesAsync();

        return NoContent();
    }

    private string GenerateOrderNumber()
    {
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd");
        var random = new Random().Next(1000, 9999);
        return $"ORD-{timestamp}-{random}";
    }

    private bool IsValidStatusTransition(OrderStatus currentStatus, OrderStatus newStatus)
    {
        return currentStatus switch
        {
            OrderStatus.Draft => newStatus is OrderStatus.Pending or OrderStatus.Cancelled,
            OrderStatus.Pending => newStatus is OrderStatus.Confirmed or OrderStatus.Cancelled,
            OrderStatus.Confirmed => newStatus is OrderStatus.InProgress or OrderStatus.Cancelled,
            OrderStatus.InProgress => newStatus is OrderStatus.Completed or OrderStatus.Cancelled,
            OrderStatus.Completed => newStatus is OrderStatus.Refunded,
            OrderStatus.Cancelled => false,
            OrderStatus.Refunded => false,
            _ => false
        };
    }
}
