<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.18" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="Ocelot" Version="23.2.2" />
    <PackageReference Include="Ocelot.Provider.Consul" Version="23.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\Aviation.Auth.OAuth2\Aviation.Auth.OAuth2.csproj" />
  </ItemGroup>

</Project>
