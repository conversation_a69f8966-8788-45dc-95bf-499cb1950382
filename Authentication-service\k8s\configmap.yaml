apiVersion: v1
kind: ConfigMap
metadata:
  name: authentication-config
  namespace: aviation-system
  labels:
    app: authentication-service
data:
  jwt-issuer: "https://auth.aviation-management.com"
  jwt-audience: "aviation-api"
  jwt-expiration-minutes: "60"
  appsettings.Production.json: |
    {
      "Logging": {
        "LogLevel": {
          "Default": "Information",
          "Microsoft.AspNetCore": "Warning",
          "Microsoft.EntityFrameworkCore": "Warning"
        }
      },
      "AllowedHosts": "*",
      "JWT": {
        "ExpirationMinutes": 60
      },
      "Serilog": {
        "MinimumLevel": {
          "Default": "Information",
          "Override": {
            "Microsoft": "Warning",
            "System": "Warning"
          }
        },
        "WriteTo": [
          {
            "Name": "Console",
            "Args": {
              "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
            }
          },
          {
            "Name": "File",
            "Args": {
              "path": "logs/authentication-service-.txt",
              "rollingInterval": "Day",
              "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
            }
          }
        ],
        "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]
      }
    }
---
apiVersion: v1
kind: Secret
metadata:
  name: authentication-secrets
  namespace: aviation-system
  labels:
    app: authentication-service
type: Opaque
stringData:
  database-connection-string: "Server=sql-server-service;Database=AviationAuthentication;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=true;"
  jwt-secret-key: "aviation-auth-super-secret-key-256-bits-minimum-length-required-for-production-change-this"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: authentication-service-account
  namespace: aviation-system
  labels:
    app: authentication-service
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: aviation-system
  name: authentication-service-role
rules:
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: authentication-service-rolebinding
  namespace: aviation-system
subjects:
- kind: ServiceAccount
  name: authentication-service-account
  namespace: aviation-system
roleRef:
  kind: Role
  name: authentication-service-role
  apiGroup: rbac.authorization.k8s.io
