namespace Aviation.Auth.OAuth2.Models;

/// <summary>
/// Defines the OAuth 2.0 scopes for B2B API access
/// </summary>
public static class B2BScopes
{
    // Partner & Customer Service scopes
    public const string PartnerRead = "partner:read";
    public const string PartnerWrite = "partner:write";
    public const string CustomerRead = "customer:read";
    public const string CustomerWrite = "customer:write";
    
    // Order Management scopes
    public const string OrderRead = "order:read";
    public const string OrderWrite = "order:write";
    public const string OrderCancel = "order:cancel";
    
    // Invoice/Finance Billing scopes
    public const string InvoiceRead = "invoice:read";
    public const string InvoiceWrite = "invoice:write";
    public const string BillingRead = "billing:read";
    
    // Product & Pricing scopes
    public const string ProductRead = "product:read";
    public const string ProductWrite = "product:write";
    public const string PricingRead = "pricing:read";
    public const string PricingWrite = "pricing:write";
    
    // Trip Estimation scopes
    public const string TripEstimate = "trip:estimate";
    public const string FlightInfoRead = "flight:read";
    public const string AirportInfoRead = "airport:read";
    
    // Document Service scopes (optional)
    public const string DocumentRead = "document:read";
    public const string DocumentWrite = "document:write";
    public const string DocumentDelete = "document:delete";
    
    // Developer App Registry scopes
    public const string AppRegistryRead = "app:read";
    public const string AppRegistryWrite = "app:write";
    public const string AppRegistryAdmin = "app:admin";
    
    // API Gateway scopes
    public const string ApiGatewayAccess = "api:access";
    
    /// <summary>
    /// Gets all available B2B scopes
    /// </summary>
    public static readonly string[] AllScopes = 
    {
        PartnerRead, PartnerWrite, CustomerRead, CustomerWrite,
        OrderRead, OrderWrite, OrderCancel,
        InvoiceRead, InvoiceWrite, BillingRead,
        ProductRead, ProductWrite, PricingRead, PricingWrite,
        TripEstimate, FlightInfoRead, AirportInfoRead,
        DocumentRead, DocumentWrite, DocumentDelete,
        AppRegistryRead, AppRegistryWrite, AppRegistryAdmin,
        ApiGatewayAccess
    };
}
