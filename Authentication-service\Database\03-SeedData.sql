-- =============================================
-- Seed Initial Data
-- =============================================

USE AviationAuthentication;

-- =============================================
-- Seed Permissions
-- =============================================

INSERT INTO Permissions (PermissionId, Name, Description, CreatedDate) VALUES 
('11111111-1111-1111-1111-111111111111', 'Create', 'Create new records', GETUTCDATE()),
('22222222-2222-2222-2222-222222222222', 'View', 'View records', GETUTCDATE()),
('33333333-3333-3333-3333-333333333333', 'Update', 'Modify existing records', GETUTCDATE()),
('44444444-4444-4444-4444-444444444444', 'Delete', 'Remove records', GETUTCDATE());

-- =============================================
-- Seed Roles
-- =============================================

INSERT INTO Roles (RoleId, RoleCode, Name, Description, IsActive, CreatedDate) VALUES
('********-0000-0000-0000-************', 'ROLE-001', 'Sales', 'Sales department role', 1, GETUTCDATE()),
('********-0000-0000-0000-************', 'ROLE-002', 'Finance', 'Finance department role', 1, GETUTCDATE()),
('********-0000-0000-0000-************', 'ROLE-003', 'Vendor', 'Vendor management role', 1, GETUTCDATE()),
('********-0000-0000-0000-************', 'ROLE-004', 'Supply', 'Supply chain role', 1, GETUTCDATE()),
('********-0000-0000-0000-************', 'ROLE-005', 'Operational', 'Operational role', 1, GETUTCDATE()),
('********-0000-0000-0000-************', 'ROLE-006', 'Non-D operational', 'Non-D operational role', 1, GETUTCDATE()),
('********-0000-0000-0000-************', 'ROLE-007', 'Sales Manager', 'Sales management role', 1, GETUTCDATE()),
('********-0000-0000-0000-************', 'ROLE-008', 'Legal', 'Legal department role', 1, GETUTCDATE()),
('********-0000-0000-0000-************', 'ROLE-009', 'Customer admin', 'Customer administration role', 1, GETUTCDATE()),
('********-0000-0000-0000-************', 'ROLE-010', 'Accounts', 'Accounts department role', 1, GETUTCDATE()),
('********-0000-0000-0000-************', 'ROLE-011', 'Tender Committee', 'Tender committee role', 1, GETUTCDATE());

-- =============================================
-- Seed Modules
-- =============================================

INSERT INTO Modules (ModuleId, Name, Description, CreatedDate) VALUES
('********-0000-0000-0000-************', 'User Management', 'User and role management', GETUTCDATE()),
('********-0000-0000-0000-************', 'Partner Management', 'Partner and customer management', GETUTCDATE()),
('********-0000-0000-0000-************', 'Order Management', 'Order processing and management', GETUTCDATE()),
('********-0000-0000-0000-************', 'Finance Management', 'Financial operations and billing', GETUTCDATE()),
('********-0000-0000-0000-************', 'Product Management', 'Product catalog and pricing', GETUTCDATE()),
('********-0000-0000-0000-************', 'Trip Management', 'Trip estimation and planning', GETUTCDATE()),
('********-0000-0000-0000-************', 'Document Management', 'Document storage and management', GETUTCDATE()),
('********-0000-0000-0000-************', 'Reports', 'Reporting and analytics', GETUTCDATE());

-- =============================================
-- Seed Entities
-- =============================================

INSERT INTO Entities (EntityId, Name, Description, CreatedDate) VALUES
('30000000-0000-0000-0000-************', 'Users', 'System users', GETUTCDATE()),
('30000000-0000-0000-0000-************', 'Roles', 'User roles', GETUTCDATE()),
('30000000-0000-0000-0000-************', 'Partners', 'Business partners', GETUTCDATE()),
('30000000-0000-0000-0000-************', 'Customers', 'Customer records', GETUTCDATE()),
('30000000-0000-0000-0000-************', 'Orders', 'Order records', GETUTCDATE()),
('30000000-0000-0000-0000-************', 'Invoices', 'Invoice records', GETUTCDATE()),
('30000000-0000-0000-0000-************', 'Products', 'Product catalog', GETUTCDATE()),
('30000000-0000-0000-0000-************', 'Documents', 'Document files', GETUTCDATE());

-- =============================================
-- Seed B2B OAuth 2.0 Scopes
-- =============================================

INSERT INTO Scopes (Name, DisplayName, Description, Category, IsRequired, IsActive, CreatedAt) VALUES 
('partner:read', 'Read Partners', 'Read partner information', 0, 0, 1, GETUTCDATE()),
('partner:write', 'Write Partners', 'Create and update partner information', 0, 0, 1, GETUTCDATE()),
('customer:read', 'Read Customers', 'Read customer information', 1, 0, 1, GETUTCDATE()),
('customer:write', 'Write Customers', 'Create and update customer information', 1, 0, 1, GETUTCDATE()),
('order:read', 'Read Orders', 'Read order information', 2, 0, 1, GETUTCDATE()),
('order:write', 'Write Orders', 'Create and update orders', 2, 0, 1, GETUTCDATE()),
('order:cancel', 'Cancel Orders', 'Cancel existing orders', 2, 0, 1, GETUTCDATE()),
('invoice:read', 'Read Invoices', 'Read invoice information', 3, 0, 1, GETUTCDATE()),
('invoice:write', 'Write Invoices', 'Create and update invoices', 3, 0, 1, GETUTCDATE()),
('billing:read', 'Read Billing', 'Read billing information', 3, 0, 1, GETUTCDATE()),
('product:read', 'Read Products', 'Read product catalog', 4, 0, 1, GETUTCDATE()),
('product:write', 'Write Products', 'Update product information', 4, 0, 1, GETUTCDATE()),
('pricing:read', 'Read Pricing', 'Read pricing information', 4, 0, 1, GETUTCDATE()),
('pricing:write', 'Write Pricing', 'Update pricing information', 4, 0, 1, GETUTCDATE()),
('trip:estimate', 'Trip Estimation', 'Get trip cost estimates', 5, 0, 1, GETUTCDATE()),
('flight:read', 'Read Flights', 'Read flight information', 5, 0, 1, GETUTCDATE()),
('airport:read', 'Read Airports', 'Read airport information', 5, 0, 1, GETUTCDATE()),
('document:read', 'Read Documents', 'Read documents', 6, 0, 1, GETUTCDATE()),
('document:write', 'Write Documents', 'Upload and update documents', 6, 0, 1, GETUTCDATE()),
('document:delete', 'Delete Documents', 'Delete documents', 6, 0, 1, GETUTCDATE()),
('app:read', 'Read Applications', 'Read application information', 7, 0, 1, GETUTCDATE()),
('app:write', 'Write Applications', 'Register and update applications', 7, 0, 1, GETUTCDATE()),
('app:admin', 'Admin Applications', 'Administrative access to app registry', 7, 0, 1, GETUTCDATE());

-- =============================================
-- Seed Test B2B Client
-- =============================================

-- Insert test client (password is BCrypt hash of "test_secret_123")
INSERT INTO Clients (ClientId, ClientSecret, ClientName, Description, ClientType, Status, CreatedAt, UpdatedAt, CreatedBy, AccessTokenLifetimeSeconds, RateLimitPerHour) 
VALUES ('aviation_test_client', '$2a$11$rQZJKZJKZJKZJKZJKZJKZOeH8vKZJKZJKZJKZJKZJKZJKZJKZJKZJK', 'Test Client', 'Default test client for development', 4, 0, GETUTCDATE(), GETUTCDATE(), 'system', 3600, 1000);

-- Get the client ID for scope assignment
DECLARE @ClientId INT = (SELECT Id FROM Clients WHERE ClientId = 'aviation_test_client');

-- Grant all scopes to test client
INSERT INTO ClientScopes (ClientId, ScopeId, GrantedAt, GrantedBy)
SELECT @ClientId, Id, GETUTCDATE(), 'system'
FROM Scopes;

PRINT 'Initial data seeded successfully.';

-- =============================================
-- Create Sample Admin User (Optional)
-- =============================================

-- Insert sample admin user
INSERT INTO Users (UserId, EmployeeId, Email, AzureAdObjectId, FirstName, LastName, IsActive, CreatedDate) 
VALUES ('00000000-0000-0000-0000-************', 'ADMIN001', '<EMAIL>', '00000000-0000-0000-0000-************', 'System', 'Administrator', 1, GETUTCDATE());

-- Assign all roles to admin user
INSERT INTO UserRoles (UserId, RoleId, AssignedDate)
SELECT '00000000-0000-0000-0000-************', RoleId, GETUTCDATE()
FROM Roles;

PRINT 'Sample admin user created successfully.';
