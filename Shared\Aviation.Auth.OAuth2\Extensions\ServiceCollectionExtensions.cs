using Aviation.Auth.OAuth2.Authorization;
using Aviation.Auth.OAuth2.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace Aviation.Auth.OAuth2.Extensions;

public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds OAuth 2.0 Client Credentials authentication and authorization services
    /// </summary>
    public static IServiceCollection AddOAuth2Authentication(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure OAuth2 options
        services.Configure<OAuth2Options>(configuration.GetSection(OAuth2Options.SectionName));
        var oauth2Options = configuration.GetSection(OAuth2Options.SectionName).Get<OAuth2Options>()
            ?? throw new InvalidOperationException("OAuth2 configuration is missing");

        // Add JWT Bearer authentication
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                options.Authority = oauth2Options.Authority;
                options.Audience = oauth2Options.Audience;
                options.RequireHttpsMetadata = oauth2Options.RequireHttpsMetadata;
                
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = oauth2Options.TokenValidation.ValidateIssuer,
                    ValidateAudience = oauth2Options.TokenValidation.ValidateAudience,
                    ValidateLifetime = oauth2Options.TokenValidation.ValidateLifetime,
                    ValidateIssuerSigningKey = oauth2Options.TokenValidation.ValidateIssuerSigningKey,
                    ValidIssuer = oauth2Options.Issuer,
                    ValidAudience = oauth2Options.Audience,
                    ClockSkew = TimeSpan.FromMinutes(oauth2Options.TokenValidation.ClockSkewMinutes)
                };

                // If signing key is provided, use it for validation
                if (!string.IsNullOrEmpty(oauth2Options.SigningKey))
                {
                    options.TokenValidationParameters.IssuerSigningKey = 
                        new SymmetricSecurityKey(Encoding.UTF8.GetBytes(oauth2Options.SigningKey));
                }

                // Configure events for logging and debugging
                options.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        Console.WriteLine($"Authentication failed: {context.Exception.Message}");
                        return Task.CompletedTask;
                    },
                    OnTokenValidated = context =>
                    {
                        Console.WriteLine($"Token validated for: {context.Principal?.Identity?.Name}");
                        return Task.CompletedTask;
                    }
                };
            });

        // Add authorization with scope-based policies
        services.AddAuthorization(options =>
        {
            // Add scope-based authorization handler
            services.AddSingleton<IAuthorizationHandler, ScopeAuthorizationHandler>();
            
            // Create policies for common scope combinations
            AddScopePolicies(options);
        });

        return services;
    }

    private static void AddScopePolicies(AuthorizationOptions options)
    {
        // Partner & Customer Service policies
        options.AddPolicy("PartnerRead", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.PartnerRead)));
        options.AddPolicy("PartnerWrite", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.PartnerWrite)));
        options.AddPolicy("CustomerRead", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.CustomerRead)));
        options.AddPolicy("CustomerWrite", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.CustomerWrite)));

        // Order Management policies
        options.AddPolicy("OrderRead", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.OrderRead)));
        options.AddPolicy("OrderWrite", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.OrderWrite)));
        options.AddPolicy("OrderCancel", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.OrderCancel)));

        // Invoice/Billing policies
        options.AddPolicy("InvoiceRead", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.InvoiceRead)));
        options.AddPolicy("BillingRead", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.BillingRead)));

        // Product & Pricing policies
        options.AddPolicy("ProductRead", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.ProductRead)));
        options.AddPolicy("PricingRead", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.PricingRead)));

        // Trip Estimation policies
        options.AddPolicy("TripEstimate", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.TripEstimate)));
        options.AddPolicy("FlightInfo", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.FlightInfoRead, B2BScopes.AirportInfoRead)));

        // Document Service policies
        options.AddPolicy("DocumentRead", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.DocumentRead)));

        // App Registry policies
        options.AddPolicy("AppRegistry", policy => 
            policy.Requirements.Add(new ScopeRequirement(B2BScopes.AppRegistryRead)));
    }
}
