using Aviation.Authentication.Api.Models;

namespace Aviation.Authentication.Api.Services;

public interface IUserService
{
    Task<User?> GetUserAsync(Guid userId);
    Task<User?> GetUserByEmailAsync(string email);
    Task<User?> GetUserByEmployeeIdAsync(string employeeId);
    Task<User?> GetUserByAzureAdObjectIdAsync(Guid azureAdObjectId);
    Task<IEnumerable<User>> GetUsersAsync(int page = 1, int pageSize = 20);
    Task<UserResponse> CreateUserAsync(CreateUserRequest request, string createdBy);
    Task<UserResponse?> UpdateUserAsync(Guid userId, UpdateUserRequest request);
    Task<bool> DeleteUserAsync(Guid userId);
    Task<bool> ActivateUserAsync(Guid userId);
    Task<bool> DeactivateUserAsync(Guid userId);
    Task<bool> LockUserAsync(Guid userId, TimeSpan lockoutDuration, string reason);
    Task<bool> UnlockUserAsync(Guid userId);
    Task<UserLoginResponse?> AuthenticateUserAsync(UserLoginRequest request, string ipAddress, string userAgent);
    Task<List<string>> GetUserPermissionsAsync(Guid userId);
    Task<bool> HasPermissionAsync(Guid userId, string permission, string? entity = null, string? module = null, string? subModule = null);
    Task LogLoginAttemptAsync(Guid? userId, string? email, string? employeeId, string ipAddress, bool success, string? failureReason = null);
}

public interface IRoleService
{
    Task<Role?> GetRoleAsync(Guid roleId);
    Task<Role?> GetRoleByCodeAsync(string roleCode);
    Task<IEnumerable<Role>> GetRolesAsync(bool activeOnly = true);
    Task<Role> CreateRoleAsync(CreateRoleRequest request);
    Task<Role?> UpdateRoleAsync(Guid roleId, UpdateRoleRequest request);
    Task<bool> DeleteRoleAsync(Guid roleId);
    Task<bool> AssignRoleToUserAsync(Guid userId, Guid roleId, string assignedBy);
    Task<bool> RemoveRoleFromUserAsync(Guid userId, Guid roleId);
    Task<IEnumerable<User>> GetUsersInRoleAsync(Guid roleId);
    Task<IEnumerable<Role>> GetUserRolesAsync(Guid userId);
}

public interface IPermissionService
{
    Task<IEnumerable<Permission>> GetPermissionsAsync();
    Task<Permission?> GetPermissionAsync(Guid permissionId);
    Task<Permission?> GetPermissionByNameAsync(string name);
    Task<Permission> CreatePermissionAsync(CreatePermissionRequest request);
    Task<Permission?> UpdatePermissionAsync(Guid permissionId, UpdatePermissionRequest request);
    Task<bool> DeletePermissionAsync(Guid permissionId);
    Task<bool> GrantPermissionToRoleAsync(Guid roleId, Guid permissionId, Guid? entityId = null, Guid? moduleId = null, Guid? subModuleId = null);
    Task<bool> RevokePermissionFromRoleAsync(Guid rolePermissionId);
    Task<IEnumerable<RolePermission>> GetRolePermissionsAsync(Guid roleId);
    Task<List<string>> GetEffectivePermissionsAsync(Guid userId);
}

public interface IModuleService
{
    Task<IEnumerable<Module>> GetModulesAsync();
    Task<Module?> GetModuleAsync(Guid moduleId);
    Task<Module> CreateModuleAsync(CreateModuleRequest request);
    Task<Module?> UpdateModuleAsync(Guid moduleId, UpdateModuleRequest request);
    Task<bool> DeleteModuleAsync(Guid moduleId);
    Task<IEnumerable<SubModule>> GetSubModulesAsync(Guid moduleId);
    Task<SubModule> CreateSubModuleAsync(Guid moduleId, CreateSubModuleRequest request);
    Task<SubModule?> UpdateSubModuleAsync(Guid subModuleId, UpdateSubModuleRequest request);
    Task<bool> DeleteSubModuleAsync(Guid subModuleId);
}

public interface IEntityService
{
    Task<IEnumerable<Entity>> GetEntitiesAsync();
    Task<Entity?> GetEntityAsync(Guid entityId);
    Task<Entity> CreateEntityAsync(CreateEntityRequest request);
    Task<Entity?> UpdateEntityAsync(Guid entityId, UpdateEntityRequest request);
    Task<bool> DeleteEntityAsync(Guid entityId);
}

// Additional DTOs
public class CreateRoleRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public List<Guid> PermissionIds { get; set; } = new();
}

public class UpdateRoleRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
}

public class CreatePermissionRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}

public class UpdatePermissionRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
}

public class CreateModuleRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}

public class UpdateModuleRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
}

public class CreateSubModuleRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}

public class UpdateSubModuleRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
}

public class CreateEntityRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
}

public class UpdateEntityRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
}

public class GrantPermissionRequest
{
    public Guid PermissionId { get; set; }
    public Guid? EntityId { get; set; }
    public Guid? ModuleId { get; set; }
    public Guid? SubModuleId { get; set; }
}

public class UserPermissionResponse
{
    public Guid UserId { get; set; }
    public string EmployeeId { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public List<string> Permissions { get; set; } = new();
    public List<RolePermissionDetail> RolePermissions { get; set; } = new();
}

public class RolePermissionDetail
{
    public string RoleName { get; set; } = string.Empty;
    public string Permission { get; set; } = string.Empty;
    public string? Entity { get; set; }
    public string? Module { get; set; }
    public string? SubModule { get; set; }
    public bool Granted { get; set; }
}
