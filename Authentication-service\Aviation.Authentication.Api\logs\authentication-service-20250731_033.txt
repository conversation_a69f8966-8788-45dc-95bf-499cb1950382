2025-07-31 15:34:09.954 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-31 15:34:10.266 +05:30 [INF] Now listening on: http://localhost:5293
2025-07-31 15:34:10.283 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-07-31 15:34:10.315 +05:30 [INF] Hosting environment: Development
2025-07-31 15:34:10.328 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-07-31 15:34:18.576 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/users - null null
2025-07-31 15:34:18.784 +05:30 [WRN] Failed to determine the https port for redirect.
2025-07-31 15:34:19.228 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetAllUsers (Aviation.Authentication.Api)'
2025-07-31 15:34:19.336 +05:30 [INF] Route matched with {action = "GetAllUsers", controller = "AzureAdTest"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.Nullable`1[System.Int32]) on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api).
2025-07-31 15:34:19.631 +05:30 [INF] Getting all Azure AD users...
2025-07-31 15:34:19.663 +05:30 [INF] Getting Azure AD users with filter: accountEnabled eq true
2025-07-31 15:34:22.767 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType8`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType7`11[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], Aviation.Authentication.Api, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-31 15:34:22.919 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.GetAllUsers (Aviation.Authentication.Api) in 3533.8789ms
2025-07-31 15:34:22.951 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetAllUsers (Aviation.Authentication.Api)'
2025-07-31 15:34:22.985 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/users - 200 null application/json; charset=utf-8 4418.2698ms
2025-07-31 15:35:51.032 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/users/detailed - null null
2025-07-31 15:35:51.114 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetAllUsersDetailed (Aviation.Authentication.Api)'
2025-07-31 15:35:51.154 +05:30 [INF] Route matched with {action = "GetAllUsersDetailed", controller = "AzureAdTest"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsersDetailed(System.String) on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api).
2025-07-31 15:35:51.206 +05:30 [INF] Getting detailed Azure AD users...
2025-07-31 15:35:51.232 +05:30 [INF] Getting Azure AD users with filter: accountEnabled eq true
2025-07-31 15:35:53.357 +05:30 [INF] Getting groups for user: "d294a608-e1fb-4dec-a9b9-7b0d667d3e22"
2025-07-31 15:35:53.806 +05:30 [INF] Getting groups for user: "be732392-5adc-4c94-8da3-56113f0a922c"
2025-07-31 15:35:54.033 +05:30 [INF] Getting groups for user: "d43da964-501d-4b45-8407-c15cd9ccf017"
2025-07-31 15:35:54.373 +05:30 [INF] Getting groups for user: "7d090fd4-4775-4c8c-bac9-ebd0d9157933"
2025-07-31 15:35:54.600 +05:30 [INF] Getting groups for user: "be45485d-98b8-4348-8ecf-37a87f363bb8"
2025-07-31 15:35:54.847 +05:30 [INF] Getting groups for user: "3f2c200c-5c2b-4620-aee9-a8815cf34f98"
2025-07-31 15:35:55.062 +05:30 [INF] Getting groups for user: "fa20d97d-c3d3-4771-a6ea-e38152780628"
2025-07-31 15:35:55.283 +05:30 [INF] Getting groups for user: "3fbb1cb1-4441-4f67-945a-7fe83bcf2bf5"
2025-07-31 15:35:55.536 +05:30 [INF] Getting groups for user: "15acd9ea-0a97-451f-b13d-a7c8f66200e5"
2025-07-31 15:35:55.798 +05:30 [INF] Getting groups for user: "16c76b00-46df-4960-bd77-82e8bcd3fc3d"
2025-07-31 15:35:56.035 +05:30 [INF] Getting groups for user: "8ecb0247-f955-4be3-aabf-20d0418bdbe8"
2025-07-31 15:35:56.256 +05:30 [INF] Getting groups for user: "197c3d67-e9f7-48cf-b4b1-ee8f24f6c21f"
2025-07-31 15:35:56.499 +05:30 [INF] Getting groups for user: "8e72c1b2-b70f-4aa3-9656-ae72e9f239c2"
2025-07-31 15:35:56.735 +05:30 [INF] Getting groups for user: "34fa3a0d-7ae7-4a6c-a4cb-23e448faca95"
2025-07-31 15:35:56.963 +05:30 [INF] Getting groups for user: "124aaca4-d0d5-4f0e-9328-3471e97f954b"
2025-07-31 15:35:57.169 +05:30 [INF] Getting groups for user: "aa6a4c66-1cff-49b3-b303-0f07560ba325"
2025-07-31 15:35:57.387 +05:30 [INF] Getting groups for user: "3661fed1-a017-41a2-a506-356e7c5187b9"
2025-07-31 15:35:57.599 +05:30 [INF] Getting groups for user: "3974e122-f8de-4bc0-b370-a09b82787e41"
2025-07-31 15:35:57.834 +05:30 [INF] Getting groups for user: "bbf6e4c8-7f0b-4bde-9740-9744c18ab54f"
2025-07-31 15:35:58.065 +05:30 [INF] Getting groups for user: "f642c7fd-8d3b-49d7-92bf-58b0ba04c8ee"
2025-07-31 15:35:58.298 +05:30 [INF] Getting groups for user: "4332e0cc-1c22-470f-8576-4e17809fa342"
2025-07-31 15:35:58.498 +05:30 [INF] Getting groups for user: "454bc262-9f58-4371-b8dc-690b83531122"
2025-07-31 15:35:58.751 +05:30 [INF] Getting groups for user: "5ca7a867-edbc-4b6d-b60b-8eeb913aac40"
2025-07-31 15:35:58.974 +05:30 [INF] Getting groups for user: "7b804bd5-4c2c-48f9-a6cd-deaf75e44443"
2025-07-31 15:35:59.205 +05:30 [INF] Getting groups for user: "959aa7ef-b2bd-4174-af5d-7da5a55b8dcc"
2025-07-31 15:35:59.436 +05:30 [INF] Getting groups for user: "2b468b62-0bbe-48ae-b8da-9f7ae0422c48"
2025-07-31 15:35:59.654 +05:30 [INF] Getting groups for user: "9f0748cc-59fa-4c51-ae22-c9fef491121f"
2025-07-31 15:35:59.872 +05:30 [INF] Getting groups for user: "eae5b7f5-ff5f-48b1-9aa1-f2e45dda799f"
2025-07-31 15:36:00.117 +05:30 [INF] Getting groups for user: "aed85fb7-0164-4f1a-b4cb-bfcbfef5271c"
2025-07-31 15:36:00.365 +05:30 [INF] Getting groups for user: "131a2042-b6f6-457d-b1f1-25d1a6ac5bda"
2025-07-31 15:36:00.612 +05:30 [INF] Getting groups for user: "90625024-652b-4878-ab1b-d4597fb6707f"
2025-07-31 15:36:00.813 +05:30 [INF] Getting groups for user: "34566b91-6f62-4332-b69e-69b6001db250"
2025-07-31 15:36:01.022 +05:30 [INF] Getting groups for user: "eb2fd252-98d0-4b57-b889-0edc6c5120d7"
2025-07-31 15:36:01.220 +05:30 [INF] Getting groups for user: "ef23d52e-3e3b-4b0b-9e51-3685b65863b3"
2025-07-31 15:36:01.430 +05:30 [INF] Getting groups for user: "fd64b97a-9b33-4657-bdfe-4a8a85e954da"
2025-07-31 15:36:01.712 +05:30 [INF] Getting groups for user: "a0203755-f59b-4308-b1a6-7974489ec420"
2025-07-31 15:36:01.920 +05:30 [INF] Getting groups for user: "634a79f2-5fb5-4343-9198-f507214bfac9"
2025-07-31 15:36:02.138 +05:30 [INF] Getting groups for user: "0dc605fb-4701-4427-ba04-bb3eed037570"
2025-07-31 15:36:02.369 +05:30 [INF] Getting groups for user: "4cb53298-e253-419a-ae37-2008f335f9d4"
2025-07-31 15:36:02.589 +05:30 [INF] Getting groups for user: "02b578d3-b8d7-4699-9069-e35ba6b6f340"
2025-07-31 15:36:02.807 +05:30 [INF] Getting groups for user: "55c0226b-403a-45e6-a6f0-28a8b813b495"
2025-07-31 15:36:03.145 +05:30 [INF] Getting groups for user: "94234ab8-548e-4621-bc6b-2a5374870e5a"
2025-07-31 15:36:03.357 +05:30 [INF] Getting groups for user: "e864367b-9605-4845-b453-eaa629e82c4c"
2025-07-31 15:36:03.563 +05:30 [INF] Getting groups for user: "1a74f48b-32bd-4ff5-929c-1d3b7b4c609f"
2025-07-31 15:36:03.796 +05:30 [INF] Getting groups for user: "e7120312-b598-44ab-a2ba-8dfd8703936b"
2025-07-31 15:36:04.417 +05:30 [INF] Getting groups for user: "7cf95f87-51f9-48a9-a5c9-8228c72542c3"
2025-07-31 15:36:04.618 +05:30 [INF] Getting groups for user: "91fd390d-db34-4abd-9201-506af91f730d"
2025-07-31 15:36:05.295 +05:30 [INF] Getting groups for user: "893075d8-fe30-4c1c-bc95-d7ba2b362823"
2025-07-31 15:36:05.533 +05:30 [INF] Getting groups for user: "f1c710c3-c063-4de8-aedb-6ea85f6b930b"
2025-07-31 15:36:05.712 +05:30 [INF] Getting groups for user: "e45e1ee8-acd4-4add-ae65-25cbfc8a0747"
2025-07-31 15:36:05.891 +05:30 [INF] Getting groups for user: "8e4bea42-7831-4dd5-93d2-af35acb651d5"
2025-07-31 15:36:06.102 +05:30 [INF] Getting groups for user: "7ed6d970-d87c-41cb-aaef-28e662d8acf5"
2025-07-31 15:36:06.335 +05:30 [INF] Getting groups for user: "02421842-69cd-48ed-8382-f6c7ccd3760b"
2025-07-31 15:36:06.530 +05:30 [INF] Getting groups for user: "8348b886-97f4-430c-9051-098eb31a2951"
2025-07-31 15:36:06.721 +05:30 [INF] Getting groups for user: "7a1b182a-e1c5-4d5f-9055-edb6f2308ffc"
2025-07-31 15:36:06.946 +05:30 [INF] Getting groups for user: "0968d453-bfcc-408f-a762-36304dca910d"
2025-07-31 15:36:07.156 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType8`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-31 15:36:07.217 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.GetAllUsersDetailed (Aviation.Authentication.Api) in 16026.6355ms
2025-07-31 15:36:07.242 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetAllUsersDetailed (Aviation.Authentication.Api)'
2025-07-31 15:36:07.264 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/users/detailed - 200 null application/json; charset=utf-8 16231.3844ms
2025-07-31 15:49:29.930 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 109
2025-07-31 15:49:30.374 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-07-31 15:49:30.432 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-07-31 15:49:31.579 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-31 15:49:31.617 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 1174.9333ms
2025-07-31 15:49:31.624 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-07-31 15:49:31.633 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 400 null application/problem+json; charset=utf-8 1706.4192ms
2025-07-31 15:49:44.361 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 121
2025-07-31 15:49:44.375 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-07-31 15:49:44.379 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-07-31 15:49:44.666 +05:30 [INF] Validating Azure AD token
2025-07-31 15:49:46.436 +05:30 [ERR] Error validating Azure AD token
Microsoft.Graph.Models.ODataErrors.ODataError: /me request is only valid with delegated authentication flow.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Me.MeRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.ValidateTokenAsync(String accessToken) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 46
2025-07-31 15:49:48.695 +05:30 [INF] Executed DbCommand (54ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-07-31 15:49:48.732 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-31 15:49:48.742 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 4354.4201ms
2025-07-31 15:49:48.749 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-07-31 15:49:48.763 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 4401.9533ms
2025-07-31 15:50:12.589 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 121
2025-07-31 15:50:12.601 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-07-31 15:50:12.606 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-07-31 15:50:12.625 +05:30 [INF] Validating Azure AD token
2025-07-31 15:50:12.870 +05:30 [ERR] Error validating Azure AD token
Microsoft.Graph.Models.ODataErrors.ODataError: /me request is only valid with delegated authentication flow.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Me.MeRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.ValidateTokenAsync(String accessToken) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 46
2025-07-31 15:50:12.951 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-07-31 15:50:12.964 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-31 15:50:12.974 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 357.3514ms
2025-07-31 15:50:12.979 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-07-31 15:50:12.986 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 396.5177ms
2025-07-31 15:53:20.620 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 121
2025-07-31 15:53:20.658 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-07-31 15:53:20.669 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-07-31 15:53:20.695 +05:30 [INF] Validating Azure AD token
2025-07-31 15:53:22.178 +05:30 [ERR] Error validating Azure AD token
Microsoft.Graph.Models.ODataErrors.ODataError: /me request is only valid with delegated authentication flow.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Me.MeRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.ValidateTokenAsync(String accessToken) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 46
2025-07-31 15:53:22.233 +05:30 [INF] Executed DbCommand (18ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-07-31 15:53:22.246 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-31 15:53:22.253 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 1565.7835ms
2025-07-31 15:53:22.258 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-07-31 15:53:22.267 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 1647.9472ms
2025-07-31 15:54:11.431 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 128
2025-07-31 15:54:11.443 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-07-31 15:54:11.451 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api).
2025-07-31 15:54:11.464 +05:30 [INF] Validating Azure AD token
2025-07-31 15:54:11.750 +05:30 [ERR] Error validating Azure AD token
Microsoft.Graph.Models.ODataErrors.ODataError: /me request is only valid with delegated authentication flow.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Me.MeRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.ValidateTokenAsync(String accessToken) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 46
2025-07-31 15:54:11.807 +05:30 [ERR] Failed executing DbCommand (30ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 4000), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-07-31 15:54:12.217 +05:30 [ERR] An exception occurred in the database while saving changes for context type 'Aviation.Authentication.Api.Data.AuthDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): String or binary data would be truncated in table 'AviationAuthentication.dbo.LoginHistory', column 'AttemptedEmployeeId'. Truncated value: 'D294A608-E1FB-4DEC-A'.
The statement has been terminated.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__209_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:a058c23c-6ee0-4a8b-a777-88b789c0f86e
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): String or binary data would be truncated in table 'AviationAuthentication.dbo.LoginHistory', column 'AttemptedEmployeeId'. Truncated value: 'D294A608-E1FB-4DEC-A'.
The statement has been terminated.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__209_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:a058c23c-6ee0-4a8b-a777-88b789c0f86e
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-31 15:54:12.251 +05:30 [ERR] Error during user login for D294A608-E1FB-4DEC-A9B9-7B0D667D3E22
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): String or binary data would be truncated in table 'AviationAuthentication.dbo.LoginHistory', column 'AttemptedEmployeeId'. Truncated value: 'D294A608-E1FB-4DEC-A'.
The statement has been terminated.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__209_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:a058c23c-6ee0-4a8b-a777-88b789c0f86e
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.UserService.LogLoginAttemptAsync(Nullable`1 userId, String email, String employeeId, String ipAddress, Boolean success, String failureReason) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\UserService.cs:line 447
   at Aviation.Authentication.Api.Controllers.AuthController.Login(UserLoginRequest request) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Controllers\AuthController.cs:line 59
2025-07-31 15:54:12.305 +05:30 [ERR] Failed executing DbCommand (11ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 4000), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid), @p8='?' (DbType = Guid), @p9='?' (Size = 255), @p10='?' (Size = 4000), @p11='?' (Size = 200), @p12='?' (Size = 50), @p13='?' (DbType = DateTime2), @p14='?' (DbType = Boolean), @p15='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7),
(@p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-07-31 15:54:12.317 +05:30 [ERR] An exception occurred in the database while saving changes for context type 'Aviation.Authentication.Api.Data.AuthDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): String or binary data would be truncated in table 'AviationAuthentication.dbo.LoginHistory', column 'AttemptedEmployeeId'. Truncated value: 'D294A608-E1FB-4DEC-A'.
The statement has been terminated.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__209_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:a058c23c-6ee0-4a8b-a777-88b789c0f86e
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): String or binary data would be truncated in table 'AviationAuthentication.dbo.LoginHistory', column 'AttemptedEmployeeId'. Truncated value: 'D294A608-E1FB-4DEC-A'.
The statement has been terminated.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__209_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:a058c23c-6ee0-4a8b-a777-88b789c0f86e
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-31 15:54:12.339 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 878.6409ms
2025-07-31 15:54:12.349 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)'
2025-07-31 15:54:12.361 +05:30 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): String or binary data would be truncated in table 'AviationAuthentication.dbo.LoginHistory', column 'AttemptedEmployeeId'. Truncated value: 'D294A608-E1FB-4DEC-A'.
The statement has been terminated.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__209_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:a058c23c-6ee0-4a8b-a777-88b789c0f86e
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.UserService.LogLoginAttemptAsync(Nullable`1 userId, String email, String employeeId, String ipAddress, Boolean success, String failureReason) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\UserService.cs:line 447
   at Aviation.Authentication.Api.Controllers.AuthController.Login(UserLoginRequest request) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Controllers\AuthController.cs:line 172
   at lambda_method22(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-31 15:54:12.454 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 500 null text/plain; charset=utf-8 1022.3378ms
2025-07-31 15:56:42.359 +05:30 [INF] Application is shutting down...
[2025-07-31 16:01:54.517 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-07-31 16:01:54.710 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 16:01:54.720 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 16:01:54.723 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 16:01:54.726 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 16:02:19.425 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 49 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:19.558 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:21.438 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:21.461 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:26.728 +05:30 INF] Executed DbCommand (73ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:26.861 +05:30 INF] Executed DbCommand (18ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name] AS [Permission], [e].[Name] AS [Entity], [m].[Name] AS [Module], [s].[Name] AS [SubModule]
FROM [UserRoles] AS [u]
INNER JOIN [RolePermissions] AS [r] ON [u].[RoleId] = [r].[RoleId]
INNER JOIN [Permissions] AS [p] ON [r].[PermissionId] = [p].[PermissionId]
LEFT JOIN [Entities] AS [e] ON [r].[EntityId] = [e].[EntityId]
LEFT JOIN [Modules] AS [m] ON [r].[ModuleId] = [m].[ModuleId]
LEFT JOIN [SubModules] AS [s] ON [r].[SubModuleId] = [s].[SubModuleId]
WHERE [u].[UserId] = @__userId_0 AND [r].[Granted] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:27.190 +05:30 INF] Executed DbCommand (49ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Boolean), @p4='?' (DbType = DateTime2), @p5='?' (DbType = DateTime2), @p6='?' (Size = 500), @p7='?' (Size = 1000), @p8='?' (Size = 500), @p9='?' (Size = 100), @p10='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AccessTokens] ([ClientId], [ExpiresAt], [IpAddress], [IsRevoked], [IssuedAt], [RevokedAt], [RevokedReason], [Scopes], [TokenHash], [TokenId], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:27.235 +05:30 INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[UserId] = @__userId_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:27.270 +05:30 INF] Executed DbCommand (7ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [LastLogin] = @p0, [ModifiedDate] = @p1
OUTPUT 1
WHERE [UserId] = @p2; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:27.319 +05:30 INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:27.341 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[RoleId], [r].[RoleCode], [r].[Name], [r].[Description], [r].[IsActive], [u].[AssignedDate]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[RoleId]
WHERE [u].[UserId] = @__userId_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:27.389 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:27.409 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Models.UserLoginResponse'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:27.455 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 5980.3322ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:27.463 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:27.484 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 200 null application/json; charset=utf-8 8078.2499ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.620 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 49 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.633 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.640 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.679 +05:30 INF] Executed DbCommand (11ms) [Parameters=[@__email_0='?' (Size = 255)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[Email] = @__email_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.704 +05:30 INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Name] AS [Permission], [e].[Name] AS [Entity], [m].[Name] AS [Module], [s].[Name] AS [SubModule]
FROM [UserRoles] AS [u]
INNER JOIN [RolePermissions] AS [r] ON [u].[RoleId] = [r].[RoleId]
INNER JOIN [Permissions] AS [p] ON [r].[PermissionId] = [p].[PermissionId]
LEFT JOIN [Entities] AS [e] ON [r].[EntityId] = [e].[EntityId]
LEFT JOIN [Modules] AS [m] ON [r].[ModuleId] = [m].[ModuleId]
LEFT JOIN [SubModules] AS [s] ON [r].[SubModuleId] = [s].[SubModuleId]
WHERE [u].[UserId] = @__userId_0 AND [r].[Granted] = CAST(1 AS bit) {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.723 +05:30 INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime2), @p2='?' (Size = 50), @p3='?' (DbType = Boolean), @p4='?' (DbType = DateTime2), @p5='?' (DbType = DateTime2), @p6='?' (Size = 500), @p7='?' (Size = 1000), @p8='?' (Size = 500), @p9='?' (Size = 100), @p10='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AccessTokens] ([ClientId], [ExpiresAt], [IpAddress], [IsRevoked], [IssuedAt], [RevokedAt], [RevokedReason], [Scopes], [TokenHash], [TokenId], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.743 +05:30 INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[UserId], [t].[AzureAdObjectId], [t].[CreatedDate], [t].[Email], [t].[EmployeeId], [t].[FailedAttempts], [t].[FirstName], [t].[IsActive], [t].[LastLogin], [t].[LastName], [t].[LockoutEnd], [t].[ModifiedDate], [t0].[UserRoleId], [t0].[AssignedDate], [t0].[RoleId], [t0].[UserId], [t0].[RoleId0], [t0].[CreatedDate], [t0].[Description], [t0].[IsActive], [t0].[ModifiedDate], [t0].[Name], [t0].[RoleCode]
FROM (
    SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
    FROM [Users] AS [u]
    WHERE [u].[UserId] = @__userId_0
) AS [t]
LEFT JOIN (
    SELECT [u0].[UserRoleId], [u0].[AssignedDate], [u0].[RoleId], [u0].[UserId], [r].[RoleId] AS [RoleId0], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
    FROM [UserRoles] AS [u0]
    INNER JOIN [Roles] AS [r] ON [u0].[RoleId] = [r].[RoleId]
) AS [t0] ON [t].[UserId] = [t0].[UserId]
ORDER BY [t].[UserId], [t0].[UserRoleId] {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.760 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = DateTime2), @p1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [LastLogin] = @p0, [ModifiedDate] = @p1
OUTPUT 1
WHERE [UserId] = @p2; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.774 +05:30 INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.794 +05:30 INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[RoleId], [r].[RoleCode], [r].[Name], [r].[Description], [r].[IsActive], [u].[AssignedDate]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[RoleId]
WHERE [u].[UserId] = @__userId_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.824 +05:30 INF] Executed DbCommand (18ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.855 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Models.UserLoginResponse'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.875 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 226.4361ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.908 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:02:35.930 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 200 null application/json; charset=utf-8 310.5339ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4I9:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4I9"}
[2025-07-31 16:03:17.475 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 128 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:17.515 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:17.527 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:17.580 +05:30 INF] Validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:20.152 +05:30 ERR] Error validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
Microsoft.Graph.Models.ODataErrors.ODataError: /me request is only valid with delegated authentication flow.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Me.MeRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.ValidateTokenAsync(String accessToken) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 46
[2025-07-31 16:03:20.309 +05:30 INF] Executed DbCommand (16ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 4000), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:20.335 +05:30 INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:20.349 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 2787.5518ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:20.365 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:20.376 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 2902.6213ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:47.243 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 128 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:47.273 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:47.291 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:47.314 +05:30 INF] Validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:48.709 +05:30 ERR] Error validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
Microsoft.Graph.Models.ODataErrors.ODataError: /me request is only valid with delegated authentication flow.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Me.MeRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.ValidateTokenAsync(String accessToken) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 46
[2025-07-31 16:03:48.734 +05:30 INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 4000), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:48.764 +05:30 INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:48.780 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 1468.4059ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:48.797 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:03:48.809 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 1565.9487ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:04:06.397 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 128 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:04:06.513 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:04:06.599 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:04:06.634 +05:30 INF] Validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:04:06.952 +05:30 ERR] Error validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
Microsoft.Graph.Models.ODataErrors.ODataError: /me request is only valid with delegated authentication flow.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Me.MeRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.ValidateTokenAsync(String accessToken) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 46
[2025-07-31 16:04:06.985 +05:30 INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 4000), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:04:07.000 +05:30 INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:04:07.013 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 381.3236ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:04:07.028 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:04:07.035 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 637.9487ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4IB:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IB"}
[2025-07-31 16:05:28.747 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users - application/json 253 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:28.862 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:28.883 +05:30 INF] Route matched with {action = "CreateUser", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserResponse]] CreateUser(Aviation.Authentication.Api.Models.CreateUserRequest) on controller Aviation.Authentication.Api.Controllers.UsersController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"36d5965a-798e-48fb-bf57-204777fef03a","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:28.965 +05:30 INF] Executed DbCommand (13ms) [Parameters=[@__request_Email_0='?' (Size = 255), @__request_EmployeeId_1='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[AzureAdObjectId], [u].[CreatedDate], [u].[Email], [u].[EmployeeId], [u].[FailedAttempts], [u].[FirstName], [u].[IsActive], [u].[LastLogin], [u].[LastName], [u].[LockoutEnd], [u].[ModifiedDate]
FROM [Users] AS [u]
WHERE [u].[Email] = @__request_Email_0 OR [u].[EmployeeId] = @__request_EmployeeId_1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"36d5965a-798e-48fb-bf57-204777fef03a","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:29.018 +05:30 INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Guid), @p2='?' (DbType = DateTime2), @p3='?' (Size = 255), @p4='?' (Size = 20), @p5='?' (DbType = Int32), @p6='?' (Size = 100), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime2), @p9='?' (Size = 100), @p10='?' (DbType = DateTime2), @p11='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([UserId], [AzureAdObjectId], [CreatedDate], [Email], [EmployeeId], [FailedAttempts], [FirstName], [IsActive], [LastLogin], [LastName], [LockoutEnd], [ModifiedDate])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"36d5965a-798e-48fb-bf57-204777fef03a","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:29.056 +05:30 INF] Executed DbCommand (4ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[RoleId], [r].[CreatedDate], [r].[Description], [r].[IsActive], [r].[ModifiedDate], [r].[Name], [r].[RoleCode]
FROM [Roles] AS [r]
WHERE [r].[RoleId] = @__get_Item_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"36d5965a-798e-48fb-bf57-204777fef03a","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:29.130 +05:30 INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Guid), @p3='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [UserRoles] ([UserRoleId], [AssignedDate], [RoleId], [UserId])
VALUES (@p0, @p1, @p2, @p3); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"36d5965a-798e-48fb-bf57-204777fef03a","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:29.156 +05:30 INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"36d5965a-798e-48fb-bf57-204777fef03a","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:29.176 +05:30 INF] Executed DbCommand (4ms) [Parameters=[@__user_UserId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[RoleId], [r].[RoleCode], [r].[Name], [r].[Description], [r].[IsActive], [u].[AssignedDate]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[RoleId]
WHERE [u].[UserId] = @__user_UserId_0 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"36d5965a-798e-48fb-bf57-204777fef03a","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:29.190 +05:30 INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"36d5965a-798e-48fb-bf57-204777fef03a","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:29.204 +05:30 INF] Executing CreatedAtActionResult, writing value of type 'Aviation.Authentication.Api.Models.UserResponse'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"36d5965a-798e-48fb-bf57-204777fef03a","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:29.236 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api) in 338.8684ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:29.245 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.UsersController.CreateUser (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:05:29.251 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users - 201 null application/json; charset=utf-8 504.2871ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4ID:********","RequestPath":"/api/users","ConnectionId":"0HNEG3M3EV4ID"}
[2025-07-31 16:06:37.954 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 123 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4IF:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IF"}
[2025-07-31 16:06:37.964 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4IF:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IF"}
[2025-07-31 16:06:37.971 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IF:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IF"}
[2025-07-31 16:06:37.980 +05:30 INF] Validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IF:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IF"}
[2025-07-31 16:06:38.387 +05:30 ERR] Error validating Azure AD token {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IF:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IF"}
Microsoft.Graph.Models.ODataErrors.ODataError: /me request is only valid with delegated authentication flow.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Me.MeRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.ValidateTokenAsync(String accessToken) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 46
[2025-07-31 16:06:38.410 +05:30 INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (Size = 255), @p2='?' (Size = 20), @p3='?' (Size = 200), @p4='?' (Size = 50), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Boolean), @p7='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [LoginHistory] ([LogId], [AttemptedEmail], [AttemptedEmployeeId], [FailureReason], [IPAddress], [LoginTime], [Success], [UserId])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IF:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IF"}
[2025-07-31 16:06:38.424 +05:30 INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"8fcb64ac-4548-4f05-bed1-c767885dc811","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEG3M3EV4IF:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IF"}
[2025-07-31 16:06:38.438 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 458.9522ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG3M3EV4IF:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IF"}
[2025-07-31 16:06:38.444 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG3M3EV4IF:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IF"}
[2025-07-31 16:06:38.452 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 401 null application/json; charset=utf-8 497.9551ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG3M3EV4IF:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEG3M3EV4IF"}
