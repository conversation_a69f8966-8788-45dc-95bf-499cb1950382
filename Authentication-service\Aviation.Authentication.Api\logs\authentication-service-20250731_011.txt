2025-07-31 11:22:32.042 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-31 11:22:33.594 +05:30 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5293: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.
 ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-07-31 11:23:01.841 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-07-31 11:23:01.970 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 11:23:01.985 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 11:23:01.989 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 11:23:01.993 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 11:23:46.635 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/health - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/health","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:46.681 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/health","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:48.477 +05:30 INF] Executing endpoint 'Health checks' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/health","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:48.489 +05:30 INF] Executed endpoint 'Health checks' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/health","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:48.496 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/health - 200 null text/plain 1863.1315ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/health","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:55.047 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 51 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:55.061 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:55.093 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"4b5d1086-1aed-4ca9-b016-c47900546a40","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:55.925 +05:30 INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"4b5d1086-1aed-4ca9-b016-c47900546a40","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:55.955 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 849.1753ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:55.963 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:23:55.970 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 400 null application/problem+json; charset=utf-8 922.855ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:10.554 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/auth/login - application/json 64 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:10.616 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:10.624 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Models.UserLoginResponse]] Login(Aviation.Authentication.Api.Models.UserLoginRequest) on controller Aviation.Authentication.Api.Controllers.AuthController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"4b5d1086-1aed-4ca9-b016-c47900546a40","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:10.642 +05:30 INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"4b5d1086-1aed-4ca9-b016-c47900546a40","ActionName":"Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:10.647 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api) in 16.4758ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:10.653 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AuthController.Login (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:10.657 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/auth/login - 400 null application/problem+json; charset=utf-8 103.6061ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/api/auth/login","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:25.395 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/oauth/token - application/x-www-form-urlencoded 101 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/oauth/token","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:25.404 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.TokenController.Token (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/oauth/token","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:25.412 +05:30 INF] Route matched with {action = "Token", controller = "Token"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Token(Aviation.Authentication.Api.Models.TokenRequest) on controller Aviation.Authentication.Api.Controllers.TokenController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"1a391125-7091-4219-b0ed-cdc23bdb12f4","ActionName":"Aviation.Authentication.Api.Controllers.TokenController.Token (Aviation.Authentication.Api)","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/oauth/token","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:25.430 +05:30 INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"1a391125-7091-4219-b0ed-cdc23bdb12f4","ActionName":"Aviation.Authentication.Api.Controllers.TokenController.Token (Aviation.Authentication.Api)","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/oauth/token","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:25.437 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.TokenController.Token (Aviation.Authentication.Api) in 20.2558ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/oauth/token","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:25.441 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.TokenController.Token (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/oauth/token","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:24:25.445 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/oauth/token - 400 null application/problem+json; charset=utf-8 50.572ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFUQEMD55A:********","RequestPath":"/oauth/token","ConnectionId":"0HNEFUQEMD55A"}
[2025-07-31 11:44:27.107 +05:30 INF] Application is shutting down... {"SourceContext":"Microsoft.Hosting.Lifetime"}
