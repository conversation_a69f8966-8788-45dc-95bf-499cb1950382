{"format": 1, "restore": {"K:\\Aviation Management System\\partner-customer-service\\Aviation.PartnerCustomer.Api\\Aviation.PartnerCustomer.Api.csproj": {}}, "projects": {"K:\\Aviation Management System\\partner-customer-service\\Aviation.PartnerCustomer.Api\\Aviation.PartnerCustomer.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "K:\\Aviation Management System\\partner-customer-service\\Aviation.PartnerCustomer.Api\\Aviation.PartnerCustomer.Api.csproj", "projectName": "Aviation.PartnerCustomer.Api", "projectPath": "K:\\Aviation Management System\\partner-customer-service\\Aviation.PartnerCustomer.Api\\Aviation.PartnerCustomer.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "K:\\Aviation Management System\\partner-customer-service\\Aviation.PartnerCustomer.Api\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.18, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}