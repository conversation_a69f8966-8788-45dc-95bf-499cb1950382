using Aviation.Auth.OAuth2.Authorization;
using Aviation.Auth.OAuth2.Models;
using Aviation.PartnerCustomer.Api.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Aviation.PartnerCustomer.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PartnersController : ControllerBase
{
    private readonly ILogger<PartnersController> _logger;

    public PartnersController(ILogger<PartnersController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Get all partners
    /// </summary>
    [HttpGet]
    [RequireScope(B2BScopes.PartnerRead)]
    public async Task<ActionResult<IEnumerable<Partner>>> GetPartners()
    {
        _logger.LogInformation("Getting all partners");
        
        // Mock data for demonstration
        var partners = new List<Partner>
        {
            new Partner
            {
                Id = 1,
                Name = "Skyline Airlines",
                CompanyCode = "SKY001",
                ContactEmail = "<EMAIL>",
                ContactPhone = "******-0123",
                Address = "123 Aviation Blvd, Airport City",
                Country = "USA",
                Type = PartnerType.Airline,
                Status = PartnerStatus.Active,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow.AddDays(-1),
                ApiKey = "sky_live_abc123",
                AllowedScopes = new List<string> { B2BScopes.PartnerRead, B2BScopes.OrderRead, B2BScopes.OrderWrite }
            },
            new Partner
            {
                Id = 2,
                Name = "Global Travel Solutions",
                CompanyCode = "GTS002",
                ContactEmail = "<EMAIL>",
                ContactPhone = "******-0456",
                Address = "456 Business Park, Travel City",
                Country = "USA",
                Type = PartnerType.TravelAgency,
                Status = PartnerStatus.Active,
                CreatedAt = DateTime.UtcNow.AddDays(-60),
                UpdatedAt = DateTime.UtcNow.AddDays(-5),
                ApiKey = "gts_live_def456",
                AllowedScopes = new List<string> { B2BScopes.PartnerRead, B2BScopes.CustomerRead, B2BScopes.CustomerWrite }
            }
        };

        return Ok(partners);
    }

    /// <summary>
    /// Get partner by ID
    /// </summary>
    [HttpGet("{id}")]
    [RequireScope(B2BScopes.PartnerRead)]
    public async Task<ActionResult<Partner>> GetPartner(int id)
    {
        _logger.LogInformation("Getting partner with ID: {PartnerId}", id);
        
        if (id == 1)
        {
            var partner = new Partner
            {
                Id = 1,
                Name = "Skyline Airlines",
                CompanyCode = "SKY001",
                ContactEmail = "<EMAIL>",
                ContactPhone = "******-0123",
                Address = "123 Aviation Blvd, Airport City",
                Country = "USA",
                Type = PartnerType.Airline,
                Status = PartnerStatus.Active,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow.AddDays(-1),
                ApiKey = "sky_live_abc123",
                AllowedScopes = new List<string> { B2BScopes.PartnerRead, B2BScopes.OrderRead, B2BScopes.OrderWrite }
            };
            return Ok(partner);
        }

        return NotFound($"Partner with ID {id} not found");
    }

    /// <summary>
    /// Create a new partner
    /// </summary>
    [HttpPost]
    [RequireScope(B2BScopes.PartnerWrite)]
    public async Task<ActionResult<Partner>> CreatePartner([FromBody] Partner partner)
    {
        _logger.LogInformation("Creating new partner: {PartnerName}", partner.Name);
        
        // In a real implementation, you would save to database
        partner.Id = new Random().Next(1000, 9999);
        partner.CreatedAt = DateTime.UtcNow;
        partner.UpdatedAt = DateTime.UtcNow;
        partner.ApiKey = $"{partner.CompanyCode.ToLower()}_live_{Guid.NewGuid():N}";
        
        return CreatedAtAction(nameof(GetPartner), new { id = partner.Id }, partner);
    }

    /// <summary>
    /// Update an existing partner
    /// </summary>
    [HttpPut("{id}")]
    [RequireScope(B2BScopes.PartnerWrite)]
    public async Task<ActionResult<Partner>> UpdatePartner(int id, [FromBody] Partner partner)
    {
        _logger.LogInformation("Updating partner with ID: {PartnerId}", id);
        
        if (id != partner.Id)
        {
            return BadRequest("Partner ID mismatch");
        }

        // In a real implementation, you would update in database
        partner.UpdatedAt = DateTime.UtcNow;
        
        return Ok(partner);
    }

    /// <summary>
    /// Delete a partner
    /// </summary>
    [HttpDelete("{id}")]
    [RequireScope(B2BScopes.PartnerWrite)]
    public async Task<ActionResult> DeletePartner(int id)
    {
        _logger.LogInformation("Deleting partner with ID: {PartnerId}", id);
        
        // In a real implementation, you would delete from database
        return NoContent();
    }
}
