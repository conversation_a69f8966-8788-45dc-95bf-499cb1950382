2025-07-31 09:33:33.687 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-31 09:33:33.852 +05:30 [INF] Now listening on: http://localhost:5293
2025-07-31 09:33:33.900 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-07-31 09:33:33.905 +05:30 [INF] Hosting environment: Development
2025-07-31 09:33:33.908 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-07-31 09:34:10.047 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/health - null null
2025-07-31 09:34:10.124 +05:30 [WRN] Failed to determine the https port for redirect.
2025-07-31 09:34:10.161 +05:30 [INF] Executing endpoint 'Health checks'
2025-07-31 09:34:10.177 +05:30 [INF] Executed endpoint 'Health checks'
2025-07-31 09:34:10.192 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/health - 200 null text/plain 146.7809ms
[2025-07-31 09:35:37.994 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-07-31 09:35:38.131 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:35:38.166 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:35:38.169 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:35:38.177 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:36:17.865 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/AzureAdTest/test-connection - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFSUCQ26EI:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFSUCQ26EI"}
[2025-07-31 09:36:17.919 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEFSUCQ26EI:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFSUCQ26EI"}
[2025-07-31 09:36:17.952 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFSUCQ26EI:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFSUCQ26EI"}
[2025-07-31 09:36:17.991 +05:30 INF] Route matched with {action = "TestConnection", controller = "AzureAdTest"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] TestConnection() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"446802d7-3934-48b4-b720-c16d6f55495a","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFSUCQ26EI:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFSUCQ26EI"}
[2025-07-31 09:36:22.588 +05:30 INF] Testing Azure AD connection... {"SourceContext":"Aviation.Authentication.Api.Controllers.AzureAdTestController","ActionId":"446802d7-3934-48b4-b720-c16d6f55495a","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFSUCQ26EI:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFSUCQ26EI"}
[2025-07-31 09:36:22.626 +05:30 INF] Getting Azure AD users with filter: none {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"446802d7-3934-48b4-b720-c16d6f55495a","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFSUCQ26EI:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFSUCQ26EI"}
[2025-07-31 09:38:06.003 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/health - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFSUCQ26EJ:********","RequestPath":"/health","ConnectionId":"0HNEFSUCQ26EJ"}
[2025-07-31 09:38:06.018 +05:30 INF] Executing endpoint 'Health checks' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFSUCQ26EJ:********","RequestPath":"/health","ConnectionId":"0HNEFSUCQ26EJ"}
[2025-07-31 09:38:06.099 +05:30 INF] Executed endpoint 'Health checks' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFSUCQ26EJ:********","RequestPath":"/health","ConnectionId":"0HNEFSUCQ26EJ"}
[2025-07-31 09:38:06.111 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/health - 200 null text/plain 107.4709ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFSUCQ26EJ:********","RequestPath":"/health","ConnectionId":"0HNEFSUCQ26EJ"}
