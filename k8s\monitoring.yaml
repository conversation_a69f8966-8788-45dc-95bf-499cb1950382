# Prometheus ServiceMonitor for all microservices
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: aviation-microservices
  namespace: aviation-system
  labels:
    app: aviation-microservices
    component: monitoring
spec:
  selector:
    matchLabels:
      component: microservice
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
---
# Network Policies for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: aviation-microservices-policy
  namespace: aviation-system
spec:
  podSelector:
    matchLabels:
      component: microservice
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    - podSelector:
        matchLabels:
          component: monitoring
    ports:
    - protocol: TCP
      port: 80
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: sql-server
    ports:
    - protocol: TCP
      port: 1433
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
---
# HorizontalPodAutoscaler for API Gateway
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: aviation-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
# HorizontalPodAutoscaler for Order Management Service
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: order-management-hpa
  namespace: aviation-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: order-management-api
  minReplicas: 2
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
# PodDisruptionBudget for high availability
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: aviation-microservices-pdb
  namespace: aviation-system
spec:
  minAvailable: 1
  selector:
    matchLabels:
      component: microservice
---
# Resource Quotas for the namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: aviation-system-quota
  namespace: aviation-system
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
