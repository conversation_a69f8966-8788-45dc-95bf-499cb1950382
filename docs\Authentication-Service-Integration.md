# Authentication Service Integration Guide

## Overview

The Aviation Management System now includes a dedicated Authentication Service that provides OAuth 2.0 Client Credentials flow for B2B API access. This service acts as the central authority for all microservices authentication.

## Authentication Service Features

### 🔐 **OAuth 2.0 Server Capabilities**
- **Client Credentials Grant** - Machine-to-machine authentication
- **JWT Token Generation** - Industry-standard JSON Web Tokens
- **Token Introspection** - RFC 7662 compliant token validation
- **Token Revocation** - RFC 7009 compliant token revocation
- **Discovery Endpoint** - RFC 8414 compliant OAuth discovery

### 🏢 **Client Management**
- **B2B Client Registration** - Self-service client application registration
- **Scope Management** - Granular permission control
- **Rate Limiting** - Per-client API rate limiting
- **Status Management** - Client activation, suspension, and revocation
- **Audit Logging** - Comprehensive audit trail

### 📊 **Database Schema**
- **Clients** - B2B client applications and credentials
- **Scopes** - Available API permissions
- **Access Tokens** - Issued token tracking
- **Audit Logs** - Security and usage auditing

## Service Endpoints

### 🔑 **OAuth 2.0 Endpoints**

| Endpoint | Purpose | Method |
|----------|---------|---------|
| `/oauth/token` | Token issuance | POST |
| `/oauth/introspect` | Token validation | POST |
| `/oauth/revoke` | Token revocation | POST |
| `/oauth/.well-known/oauth-authorization-server` | Discovery | GET |
| `/oauth/.well-known/jwks.json` | Public keys | GET |

### 🛠️ **Management API Endpoints**

| Endpoint | Purpose | Method | Auth Required |
|----------|---------|---------|---------------|
| `/api/clients` | List clients | GET | ✅ |
| `/api/clients` | Create client | POST | ✅ |
| `/api/clients/{id}` | Get client | GET | ✅ |
| `/api/clients/{id}` | Update client | PUT | ✅ |
| `/api/clients/{id}/scopes` | Update scopes | PUT | ✅ |
| `/api/clients/{id}/status` | Update status | PATCH | ✅ |
| `/api/scopes` | List scopes | GET | ✅ |

## Integration with Microservices

### 🔧 **Configuration Updates**

All microservices have been updated to use the Authentication Service:

```json
{
  "OAuth2": {
    "Authority": "https://auth.aviation-management.com",
    "Audience": "aviation-api",
    "Issuer": "https://auth.aviation-management.com",
    "SigningKey": "aviation-auth-super-secret-key-256-bits-minimum-length-required-for-production",
    "RequireHttpsMetadata": true,
    "TokenValidation": {
      "ValidateLifetime": true,
      "ValidateIssuer": true,
      "ValidateAudience": true,
      "ValidateIssuerSigningKey": true,
      "ClockSkewMinutes": 5
    }
  }
}
```

### 🐳 **Docker Compose Integration**

The Authentication Service is now included in the development environment:

```yaml
authentication-service:
  build:
    context: ./Authentication-service/Aviation.Authentication.Api
    dockerfile: Dockerfile
  container_name: aviation-authentication
  environment:
    - ASPNETCORE_ENVIRONMENT=Development
    - JWT__Issuer=http://localhost:8080
    - JWT__Audience=aviation-api-dev
  ports:
    - "8080:80"
```

### ☸️ **Kubernetes Deployment**

Production deployment includes:
- **Dedicated namespace** - `aviation-system`
- **ConfigMaps** - Environment-specific configuration
- **Secrets** - JWT signing keys and database credentials
- **Ingress** - External access with SSL termination
- **Service Account** - RBAC permissions

## Available Scopes

### 📋 **B2B API Scopes**

| Category | Scopes | Description |
|----------|--------|-------------|
| **Partner** | `partner:read`, `partner:write` | Partner management |
| **Customer** | `customer:read`, `customer:write` | Customer management |
| **Order** | `order:read`, `order:write`, `order:cancel` | Order processing |
| **Finance** | `invoice:read`, `invoice:write`, `billing:read` | Financial operations |
| **Product** | `product:read`, `product:write`, `pricing:read`, `pricing:write` | Product catalog |
| **Trip** | `trip:estimate`, `flight:read`, `airport:read` | Trip planning |
| **Document** | `document:read`, `document:write`, `document:delete` | Document management |
| **Registry** | `app:read`, `app:write`, `app:admin` | App registration |

## Client Onboarding Process

### 1️⃣ **Application Registration**
```bash
curl -X POST https://auth.aviation-management.com/api/clients \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {admin-token}" \
  -d '{
    "clientName": "Skyline Airlines API Client",
    "description": "Production API integration for Skyline Airlines",
    "clientType": "Airline",
    "scopes": ["partner:read", "order:read", "order:write"],
    "accessTokenLifetimeSeconds": 3600,
    "rateLimitPerHour": 1000
  }'
```

### 2️⃣ **Client Credentials Response**
```json
{
  "id": 123,
  "clientId": "skyline_airlines_1234567890",
  "clientSecret": "abc123def456ghi789jkl012mno345pqr678stu901vwx234yz",
  "clientName": "Skyline Airlines API Client",
  "status": "PendingApproval",
  "scopes": ["partner:read", "order:read", "order:write"]
}
```

### 3️⃣ **Token Request**
```bash
curl -X POST https://auth.aviation-management.com/oauth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials&client_id=skyline_airlines_1234567890&client_secret=abc123def456ghi789jkl012mno345pqr678stu901vwx234yz&scope=partner:read order:read order:write"
```

### 4️⃣ **Token Response**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "partner:read order:read order:write",
  "token_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

## Security Features

### 🔒 **Security Measures**
- **BCrypt Password Hashing** - Secure client secret storage
- **JWT Token Signing** - HMAC-SHA256 signature verification
- **Rate Limiting** - Per-client request throttling
- **IP Restrictions** - Optional IP allowlisting
- **Token Revocation** - Immediate token invalidation
- **Audit Logging** - Complete security audit trail

### 🛡️ **Production Security**
- **HTTPS Only** - All communication encrypted
- **Secret Management** - Kubernetes secrets for sensitive data
- **Non-root Containers** - Security-hardened container images
- **Network Policies** - Kubernetes network segmentation
- **RBAC** - Role-based access control

## Monitoring and Observability

### 📈 **Metrics**
- Token issuance rates
- Authentication success/failure rates
- Client usage statistics
- Rate limit violations

### 📝 **Logging**
- Structured logging with Serilog
- Audit trail for all operations
- Security event logging
- Performance metrics

### 🔍 **Health Checks**
- Database connectivity
- JWT signing capability
- Service availability

## Development and Testing

### 🧪 **Test Client**
A default test client is automatically created:
- **Client ID**: `aviation_test_client`
- **Client Secret**: `test_secret_123`
- **Scopes**: All available scopes
- **Status**: Active

### 🚀 **Local Development**
```bash
# Start all services including authentication
./scripts/dev-setup.sh

# Authentication service available at:
# http://localhost:8080
```

### 📚 **API Documentation**
- **Swagger UI**: `http://localhost:8080/swagger`
- **Discovery Endpoint**: `http://localhost:8080/oauth/.well-known/oauth-authorization-server`

## Migration from Shared Library

### ✅ **Benefits of Dedicated Service**
1. **Centralized Management** - Single point for client management
2. **Scalability** - Independent scaling of auth service
3. **Security** - Dedicated security hardening
4. **Compliance** - OAuth 2.0 standard compliance
5. **Monitoring** - Dedicated auth metrics and logging

### 🔄 **Migration Steps**
1. Deploy Authentication Service
2. Update microservice configurations
3. Migrate existing clients
4. Update API documentation
5. Test B2B integrations

The Authentication Service provides a robust, scalable, and secure foundation for B2B API access in the Aviation Management System.
