{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=DESKTOP-MR5E5LR;Initial Catalog=AviationAuthentication;Integrated Security=True;TrustServerCertificate=True;Connection Timeout=30;"}, "JWT": {"SecretKey": "aviation-auth-super-secret-key-256-bits-minimum-length-required-for-production", "Issuer": "https://auth.aviation-management.com", "Audience": "aviation-api", "ExpirationMinutes": 60}, "AzureAd": {"TenantId": "14158288-a340-4380-88ed-a8989a932425", "ClientId": "65567050-fbad-4326-b653-aa1f30edaa71", "ClientSecret": "****************************************", "Instance": "https://login.microsoftonline.com/", "GraphApiUrl": "https://graph.microsoft.com/", "Scopes": ["User.Read.All", "Directory.Read.All", "Group.Read.All"]}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/authentication-service-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}