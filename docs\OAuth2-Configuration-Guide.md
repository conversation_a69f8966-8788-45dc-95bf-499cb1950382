# OAuth 2.0 Configuration Guide

## Environment Configuration

### Development Environment

```json
{
  "OAuth2": {
    "Authority": "https://dev-auth.aviation-management.com",
    "Audience": "aviation-api-dev",
    "Issuer": "https://dev-auth.aviation-management.com",
    "SigningKey": "dev-signing-key-256-bits-minimum-length-required",
    "RequireHttpsMetadata": false,
    "TokenValidation": {
      "ValidateLifetime": true,
      "ValidateIssuer": true,
      "ValidateAudience": true,
      "ValidateIssuerSigningKey": true,
      "ClockSkewMinutes": 5
    }
  }
}
```

### Production Environment

```json
{
  "OAuth2": {
    "Authority": "https://auth.aviation-management.com",
    "Audience": "aviation-api",
    "Issuer": "https://auth.aviation-management.com",
    "SigningKey": "prod-signing-key-256-bits-minimum-length-required",
    "RequireHttpsMetadata": true,
    "TokenValidation": {
      "ValidateLifetime": true,
      "ValidateIssuer": true,
      "ValidateAudience": true,
      "ValidateIssuerSigningKey": true,
      "ClockSkewMinutes": 2
    }
  }
}
```

## Service Port Configuration

| Service | Development Port | Production Port |
|---------|------------------|-----------------|
| API Gateway | 5000 | 443 (HTTPS) |
| Partner & Customer Service | 5001 | 8001 |
| Order Management Service | 5002 | 8002 |
| Finance Billing Service | 5003 | 8003 |
| Product Pricing Service | 5004 | 8004 |
| Trip Estimation Service | 5005 | 8005 |
| Document Service | 5006 | 8006 |
| Developer App Registry | 5007 | 8007 |

## Environment Variables

### Required Environment Variables

```bash
# OAuth 2.0 Configuration
OAUTH2_AUTHORITY=https://auth.aviation-management.com
OAUTH2_AUDIENCE=aviation-api
OAUTH2_ISSUER=https://auth.aviation-management.com
OAUTH2_SIGNING_KEY=your-256-bit-signing-key

# Database Configuration
CONNECTION_STRING=Server=localhost;Database=AviationDB;Trusted_Connection=true;

# Service Discovery (if using Consul)
CONSUL_HOST=localhost
CONSUL_PORT=8500

# Logging
LOG_LEVEL=Information
```

### Docker Environment Variables

```yaml
# docker-compose.yml
version: '3.8'
services:
  api-gateway:
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - OAUTH2_AUTHORITY=https://auth.aviation-management.com
      - OAUTH2_AUDIENCE=aviation-api
      - OAUTH2_SIGNING_KEY=${OAUTH2_SIGNING_KEY}
    ports:
      - "5000:80"
      
  partner-customer-service:
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - OAUTH2_AUTHORITY=https://auth.aviation-management.com
      - OAUTH2_AUDIENCE=aviation-api
      - OAUTH2_SIGNING_KEY=${OAUTH2_SIGNING_KEY}
    ports:
      - "5001:80"
```

## Client Registration Examples

### Sample Client Registration

```json
{
  "client_id": "aviation_partner_001",
  "client_secret": "super_secure_secret_key_here",
  "client_name": "Skyline Airlines Integration",
  "grant_types": ["client_credentials"],
  "scopes": [
    "partner:read",
    "order:read",
    "order:write",
    "invoice:read",
    "trip:estimate"
  ],
  "token_endpoint_auth_method": "client_secret_basic",
  "access_token_lifetime": 3600,
  "refresh_token_lifetime": 0
}
```

### Scope Mapping by Partner Type

#### Airline Partners
```json
{
  "default_scopes": [
    "partner:read",
    "order:read",
    "order:write",
    "order:cancel",
    "invoice:read",
    "trip:estimate",
    "flight:read",
    "airport:read"
  ]
}
```

#### Travel Agencies
```json
{
  "default_scopes": [
    "partner:read",
    "customer:read",
    "customer:write",
    "order:read",
    "order:write",
    "trip:estimate",
    "product:read",
    "pricing:read"
  ]
}
```

#### Corporate Clients
```json
{
  "default_scopes": [
    "customer:read",
    "customer:write",
    "order:read",
    "order:write",
    "invoice:read",
    "trip:estimate",
    "document:read",
    "document:write"
  ]
}
```

## Security Configuration

### JWT Token Configuration

```json
{
  "TokenOptions": {
    "Issuer": "https://auth.aviation-management.com",
    "Audience": "aviation-api",
    "AccessTokenLifetime": 3600,
    "SigningAlgorithm": "HS256",
    "ClockSkew": 300
  }
}
```

### CORS Configuration

```json
{
  "Cors": {
    "AllowedOrigins": [
      "https://partner-portal.aviation-management.com",
      "https://dev-portal.aviation-management.com"
    ],
    "AllowedMethods": ["GET", "POST", "PUT", "DELETE"],
    "AllowedHeaders": ["Authorization", "Content-Type", "X-Request-ID"],
    "ExposedHeaders": ["X-RateLimit-Limit", "X-RateLimit-Remaining"]
  }
}
```

## Monitoring and Logging

### Application Insights Configuration

```json
{
  "ApplicationInsights": {
    "InstrumentationKey": "your-app-insights-key",
    "EnableAdaptiveSampling": true,
    "EnableQuickPulseMetricStream": true
  }
}
```

### Structured Logging

```json
{
  "Serilog": {
    "MinimumLevel": "Information",
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/aviation-api-.txt",
          "rollingInterval": "Day"
        }
      },
      {
        "Name": "ApplicationInsights",
        "Args": {
          "instrumentationKey": "your-app-insights-key"
        }
      }
    ],
    "Properties": {
      "Application": "Aviation.Management.API"
    }
  }
}
```

## Health Checks Configuration

```csharp
// In Program.cs
builder.Services.AddHealthChecks()
    .AddCheck("oauth2", () => HealthCheckResult.Healthy("OAuth2 is configured"))
    .AddCheck("database", () => HealthCheckResult.Healthy("Database is accessible"))
    .AddCheck("external-apis", () => HealthCheckResult.Healthy("External APIs are accessible"));

app.MapHealthChecks("/health");
```

## Deployment Checklist

- [ ] OAuth 2.0 signing keys configured
- [ ] Environment-specific configuration files deployed
- [ ] Database connection strings configured
- [ ] HTTPS certificates installed
- [ ] Rate limiting configured
- [ ] Monitoring and logging enabled
- [ ] Health checks configured
- [ ] CORS policies configured
- [ ] Client credentials registered
- [ ] API documentation updated
