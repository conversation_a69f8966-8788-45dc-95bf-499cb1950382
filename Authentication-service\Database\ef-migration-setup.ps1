# =============================================
# Entity Framework Migration Setup Script
# =============================================

param(
    [Parameter(Mandatory=$true)]
    [string]$ConnectionString
)

Write-Host "🚀 Setting up Entity Framework Migrations..." -ForegroundColor Green

# Navigate to the project directory
$projectPath = "..\Aviation.Authentication.Api"
Set-Location $projectPath

try {
    # Install EF Core tools if not already installed
    Write-Host "🔧 Installing Entity Framework Core tools..." -ForegroundColor Yellow
    dotnet tool install --global dotnet-ef --version 8.0.0

    # Update connection string in appsettings.json
    Write-Host "📝 Updating connection string..." -ForegroundColor Yellow
    $appsettingsPath = "appsettings.json"
    $appsettings = Get-Content $appsettingsPath | ConvertFrom-Json
    $appsettings.ConnectionStrings.DefaultConnection = $ConnectionString
    $appsettings | ConvertTo-Json -Depth 10 | Set-Content $appsettingsPath

    # Create initial migration
    Write-Host "📋 Creating initial migration..." -ForegroundColor Yellow
    dotnet ef migrations add InitialCreate --context AuthDbContext

    # Update database
    Write-Host "🗄️ Updating database..." -ForegroundColor Yellow
    dotnet ef database update --context AuthDbContext

    Write-Host "✅ Entity Framework setup completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📊 Next Steps:" -ForegroundColor Cyan
    Write-Host "   1. Update your appsettings.json with the correct connection string" -ForegroundColor White
    Write-Host "   2. Run the application: dotnet run" -ForegroundColor White
    Write-Host "   3. Test the API endpoints" -ForegroundColor White

} catch {
    Write-Host "❌ Error during EF setup: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✅ Setup completed!" -ForegroundColor Green
