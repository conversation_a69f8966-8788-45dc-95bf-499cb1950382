using Aviation.Authentication.Api.Models;
using Microsoft.EntityFrameworkCore;

namespace Aviation.Authentication.Api.Data;

public class AuthDbContext : DbContext
{
    public AuthDbContext(DbContextOptions<AuthDbContext> options) : base(options)
    {
    }

    // B2B OAuth 2.0 tables
    public DbSet<Client> Clients { get; set; }
    public DbSet<Scope> Scopes { get; set; }
    public DbSet<ClientScope> ClientScopes { get; set; }
    public DbSet<AccessToken> AccessTokens { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }

    // User management tables
    public DbSet<User> Users { get; set; }
    public DbSet<PasswordReset> PasswordResets { get; set; }
    public DbSet<LoginHistory> LoginHistories { get; set; }

    // RBAC tables
    public DbSet<Role> Roles { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<Entity> Entities { get; set; }
    public DbSet<Module> Modules { get; set; }
    public DbSet<SubModule> SubModules { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<RolePermission> RolePermissions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Client entity
        modelBuilder.Entity<Client>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ClientId).IsRequired().HasMaxLength(100);
            entity.HasIndex(e => e.ClientId).IsUnique();
            
            entity.Property(e => e.ClientSecret).IsRequired().HasMaxLength(500);
            entity.Property(e => e.ClientName).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.CreatedBy).HasMaxLength(100);
            entity.Property(e => e.AllowedIpAddresses).HasMaxLength(1000);
            entity.Property(e => e.WebhookUrl).HasMaxLength(500);
            entity.Property(e => e.WebhookSecret).HasMaxLength(200);
            
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            // Configure relationships
            entity.HasMany(e => e.ClientScopes)
                  .WithOne(e => e.Client)
                  .HasForeignKey(e => e.ClientId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasMany(e => e.AccessTokens)
                  .WithOne(e => e.Client)
                  .HasForeignKey(e => e.ClientId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasMany(e => e.AuditLogs)
                  .WithOne(e => e.Client)
                  .HasForeignKey(e => e.ClientId)
                  .OnDelete(DeleteBehavior.SetNull);
        });

        // Configure Scope entity
        modelBuilder.Entity<Scope>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.HasIndex(e => e.Name).IsUnique();
            
            entity.Property(e => e.DisplayName).HasMaxLength(200);
            entity.Property(e => e.Description).HasMaxLength(500);
            
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            entity.HasMany(e => e.ClientScopes)
                  .WithOne(e => e.Scope)
                  .HasForeignKey(e => e.ScopeId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure ClientScope entity (many-to-many)
        modelBuilder.Entity<ClientScope>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => new { e.ClientId, e.ScopeId }).IsUnique();
            
            entity.Property(e => e.GrantedBy).HasMaxLength(100);
            entity.Property(e => e.GrantedAt).HasDefaultValueSql("GETUTCDATE()");
        });

        // Configure AccessToken entity
        modelBuilder.Entity<AccessToken>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TokenId).IsRequired().HasMaxLength(100);
            entity.HasIndex(e => e.TokenId).IsUnique();
            
            entity.Property(e => e.TokenHash).IsRequired().HasMaxLength(500);
            entity.Property(e => e.IpAddress).HasMaxLength(50);
            entity.Property(e => e.UserAgent).HasMaxLength(500);
            entity.Property(e => e.Scopes).HasMaxLength(1000);
            entity.Property(e => e.RevokedReason).HasMaxLength(500);
            
            entity.Property(e => e.IssuedAt).HasDefaultValueSql("GETUTCDATE()");
            
            // Index for cleanup of expired tokens
            entity.HasIndex(e => e.ExpiresAt);
            entity.HasIndex(e => e.IsRevoked);
        });

        // Configure AuditLog entity
        modelBuilder.Entity<AuditLog>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Action).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Resource).HasMaxLength(200);
            entity.Property(e => e.Details).HasMaxLength(2000);
            entity.Property(e => e.IpAddress).HasMaxLength(50);
            entity.Property(e => e.UserAgent).HasMaxLength(500);
            entity.Property(e => e.ErrorMessage).HasMaxLength(1000);
            
            entity.Property(e => e.Timestamp).HasDefaultValueSql("GETUTCDATE()");
            
            // Indexes for querying
            entity.HasIndex(e => e.Timestamp);
            entity.HasIndex(e => e.ClientId);
            entity.HasIndex(e => e.Action);
        });

        // Configure User management entities
        ConfigureUserEntities(modelBuilder);

        // Configure RBAC entities
        ConfigureRBACEntities(modelBuilder);

        // Seed data
        SeedData(modelBuilder);
    }

    private void SeedData(ModelBuilder modelBuilder)
    {
        // Seed default scopes
        var scopes = new[]
        {
            new Scope { Id = 1, Name = "partner:read", DisplayName = "Read Partners", Description = "Read partner information", Category = ScopeCategory.Partner, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 2, Name = "partner:write", DisplayName = "Write Partners", Description = "Create and update partner information", Category = ScopeCategory.Partner, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 3, Name = "customer:read", DisplayName = "Read Customers", Description = "Read customer information", Category = ScopeCategory.Customer, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 4, Name = "customer:write", DisplayName = "Write Customers", Description = "Create and update customer information", Category = ScopeCategory.Customer, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 5, Name = "order:read", DisplayName = "Read Orders", Description = "Read order information", Category = ScopeCategory.Order, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 6, Name = "order:write", DisplayName = "Write Orders", Description = "Create and update orders", Category = ScopeCategory.Order, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 7, Name = "order:cancel", DisplayName = "Cancel Orders", Description = "Cancel existing orders", Category = ScopeCategory.Order, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 8, Name = "invoice:read", DisplayName = "Read Invoices", Description = "Read invoice information", Category = ScopeCategory.Finance, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 9, Name = "invoice:write", DisplayName = "Write Invoices", Description = "Create and update invoices", Category = ScopeCategory.Finance, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 10, Name = "billing:read", DisplayName = "Read Billing", Description = "Read billing information", Category = ScopeCategory.Finance, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 11, Name = "product:read", DisplayName = "Read Products", Description = "Read product catalog", Category = ScopeCategory.Product, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 12, Name = "product:write", DisplayName = "Write Products", Description = "Update product information", Category = ScopeCategory.Product, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 13, Name = "pricing:read", DisplayName = "Read Pricing", Description = "Read pricing information", Category = ScopeCategory.Product, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 14, Name = "pricing:write", DisplayName = "Write Pricing", Description = "Update pricing information", Category = ScopeCategory.Product, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 15, Name = "trip:estimate", DisplayName = "Trip Estimation", Description = "Get trip cost estimates", Category = ScopeCategory.Trip, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 16, Name = "flight:read", DisplayName = "Read Flights", Description = "Read flight information", Category = ScopeCategory.Trip, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 17, Name = "airport:read", DisplayName = "Read Airports", Description = "Read airport information", Category = ScopeCategory.Trip, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 18, Name = "document:read", DisplayName = "Read Documents", Description = "Read documents", Category = ScopeCategory.Document, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 19, Name = "document:write", DisplayName = "Write Documents", Description = "Upload and update documents", Category = ScopeCategory.Document, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 20, Name = "document:delete", DisplayName = "Delete Documents", Description = "Delete documents", Category = ScopeCategory.Document, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 21, Name = "app:read", DisplayName = "Read Applications", Description = "Read application information", Category = ScopeCategory.Registry, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 22, Name = "app:write", DisplayName = "Write Applications", Description = "Register and update applications", Category = ScopeCategory.Registry, CreatedAt = DateTime.UtcNow },
            new Scope { Id = 23, Name = "app:admin", DisplayName = "Admin Applications", Description = "Administrative access to app registry", Category = ScopeCategory.Registry, CreatedAt = DateTime.UtcNow }
        };

        modelBuilder.Entity<Scope>().HasData(scopes);

        // Seed a default client for testing
        modelBuilder.Entity<Client>().HasData(new Client
        {
            Id = 1,
            ClientId = "aviation_test_client",
            ClientSecret = BCrypt.Net.BCrypt.HashPassword("test_secret_123"), // Hashed
            ClientName = "Test Client",
            Description = "Default test client for development",
            ClientType = ClientType.Internal,
            Status = ClientStatus.Active,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedBy = "system",
            AccessTokenLifetimeSeconds = 3600,
            RateLimitPerHour = 1000
        });

        // Grant all scopes to test client
        var clientScopes = scopes.Select((scope, index) => new ClientScope
        {
            Id = index + 1,
            ClientId = 1,
            ScopeId = scope.Id,
            GrantedAt = DateTime.UtcNow,
            GrantedBy = "system"
        }).ToArray();

        modelBuilder.Entity<ClientScope>().HasData(clientScopes);

        // Seed RBAC data
        SeedRBACData(modelBuilder);
    }

    private void ConfigureUserEntities(ModelBuilder modelBuilder)
    {
        // Configure User entity
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.UserId);
            entity.Property(e => e.EmployeeId).IsRequired().HasMaxLength(20);
            entity.HasIndex(e => e.EmployeeId).IsUnique();

            entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
            entity.HasIndex(e => e.Email).IsUnique();

            entity.Property(e => e.AzureAdObjectId).IsRequired();
            entity.HasIndex(e => e.AzureAdObjectId).IsUnique();

            entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");

            // Configure relationships
            entity.HasMany(e => e.UserRoles)
                  .WithOne(e => e.User)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.LoginHistories)
                  .WithOne(e => e.User)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.SetNull);

            entity.HasMany(e => e.PasswordResets)
                  .WithOne(e => e.User)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure PasswordReset entity
        modelBuilder.Entity<PasswordReset>(entity =>
        {
            entity.HasKey(e => e.RequestId);
            entity.Property(e => e.Token).IsRequired().HasMaxLength(100);
            entity.HasIndex(e => e.Token).IsUnique();

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");
        });

        // Configure LoginHistory entity
        modelBuilder.Entity<LoginHistory>(entity =>
        {
            entity.ToTable("LoginHistory");
            entity.HasKey(e => e.LogId);
            entity.Property(e => e.AttemptedEmail).HasMaxLength(255);
            entity.Property(e => e.AttemptedEmployeeId).HasMaxLength(20);
            entity.Property(e => e.IPAddress).HasMaxLength(50);
            entity.Property(e => e.FailureReason).HasMaxLength(200);

            entity.Property(e => e.LoginTime).HasDefaultValueSql("GETUTCDATE()");

            // Indexes for performance
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.LoginTime);
        });
    }

    private void ConfigureRBACEntities(ModelBuilder modelBuilder)
    {
        // Configure Role entity
        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.RoleId);
            entity.Property(e => e.RoleCode).IsRequired().HasMaxLength(20);
            entity.HasIndex(e => e.RoleCode).IsUnique();

            entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
            entity.HasIndex(e => e.Name).IsUnique();

            entity.Property(e => e.Description).HasMaxLength(200);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");

            entity.HasMany(e => e.UserRoles)
                  .WithOne(e => e.Role)
                  .HasForeignKey(e => e.RoleId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.RolePermissions)
                  .WithOne(e => e.Role)
                  .HasForeignKey(e => e.RoleId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure UserRole entity
        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(e => e.UserRoleId);
            entity.HasIndex(e => new { e.UserId, e.RoleId }).IsUnique();
            entity.Property(e => e.AssignedDate).HasDefaultValueSql("GETUTCDATE()");
        });

        // Configure Entity entity
        modelBuilder.Entity<Entity>(entity =>
        {
            entity.HasKey(e => e.EntityId);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
            entity.HasIndex(e => e.Name).IsUnique();
            entity.Property(e => e.Description).HasMaxLength(200);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");
        });

        // Configure Module entity
        modelBuilder.Entity<Module>(entity =>
        {
            entity.HasKey(e => e.ModuleId);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Description).HasMaxLength(200);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");

            entity.HasMany(e => e.SubModules)
                  .WithOne(e => e.Module)
                  .HasForeignKey(e => e.ModuleId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure SubModule entity
        modelBuilder.Entity<SubModule>(entity =>
        {
            entity.HasKey(e => e.SubModuleId);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Description).HasMaxLength(200);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");

            entity.HasIndex(e => new { e.ModuleId, e.Name }).IsUnique();
        });

        // Configure Permission entity
        modelBuilder.Entity<Permission>(entity =>
        {
            entity.HasKey(e => e.PermissionId);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(20);
            entity.HasIndex(e => e.Name).IsUnique();
            entity.Property(e => e.Description).HasMaxLength(100);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");
        });

        // Configure RolePermission entity
        modelBuilder.Entity<RolePermission>(entity =>
        {
            entity.HasKey(e => e.RolePermissionId);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");

            // Indexes for performance
            entity.HasIndex(e => e.RoleId);
            entity.HasIndex(e => e.ModuleId);
            entity.HasIndex(e => e.SubModuleId);
            entity.HasIndex(e => e.EntityId);
            entity.HasIndex(e => e.PermissionId);
        });
    }

    private void SeedRBACData(ModelBuilder modelBuilder)
    {
        // Seed permissions
        var permissions = new[]
        {
            new Permission { PermissionId = Guid.Parse("11111111-1111-1111-1111-111111111111"), Name = "Create", Description = "Create new records", CreatedDate = DateTime.UtcNow },
            new Permission { PermissionId = Guid.Parse("*************-2222-2222-************"), Name = "View", Description = "View records", CreatedDate = DateTime.UtcNow },
            new Permission { PermissionId = Guid.Parse("*************-3333-3333-************"), Name = "Update", Description = "Modify existing records", CreatedDate = DateTime.UtcNow },
            new Permission { PermissionId = Guid.Parse("*************-4444-4444-************"), Name = "Delete", Description = "Remove records", CreatedDate = DateTime.UtcNow }
        };
        modelBuilder.Entity<Permission>().HasData(permissions);

        // Seed roles
        var roles = new[]
        {
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-************"), RoleCode = "ROLE-001", Name = "Sales", Description = "Sales department role", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-************"), RoleCode = "ROLE-002", Name = "Finance", Description = "Finance department role", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-************"), RoleCode = "ROLE-003", Name = "Vendor", Description = "Vendor management role", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-000000000004"), RoleCode = "ROLE-004", Name = "Supply", Description = "Supply chain role", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-000000000005"), RoleCode = "ROLE-005", Name = "Operational", Description = "Operational role", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-000000000006"), RoleCode = "ROLE-006", Name = "Non-D operational", Description = "Non-D operational role", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-000000000007"), RoleCode = "ROLE-007", Name = "Sales Manager", Description = "Sales management role", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-************"), RoleCode = "ROLE-008", Name = "Legal", Description = "Legal department role", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-************"), RoleCode = "ROLE-009", Name = "Customer admin", Description = "Customer administration role", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-************"), RoleCode = "ROLE-010", Name = "Accounts", Description = "Accounts department role", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Role { RoleId = Guid.Parse("********-0000-0000-0000-************"), RoleCode = "ROLE-011", Name = "Tender Committee", Description = "Tender committee role", IsActive = true, CreatedDate = DateTime.UtcNow }
        };
        modelBuilder.Entity<Role>().HasData(roles);

        // Seed modules
        var modules = new[]
        {
            new Module { ModuleId = Guid.Parse("********-0000-0000-0000-************"), Name = "User Management", Description = "User and role management", CreatedDate = DateTime.UtcNow },
            new Module { ModuleId = Guid.Parse("********-0000-0000-0000-************"), Name = "Partner Management", Description = "Partner and customer management", CreatedDate = DateTime.UtcNow },
            new Module { ModuleId = Guid.Parse("********-0000-0000-0000-************"), Name = "Order Management", Description = "Order processing and management", CreatedDate = DateTime.UtcNow },
            new Module { ModuleId = Guid.Parse("********-0000-0000-0000-000000000004"), Name = "Finance Management", Description = "Financial operations and billing", CreatedDate = DateTime.UtcNow },
            new Module { ModuleId = Guid.Parse("********-0000-0000-0000-000000000005"), Name = "Product Management", Description = "Product catalog and pricing", CreatedDate = DateTime.UtcNow },
            new Module { ModuleId = Guid.Parse("********-0000-0000-0000-000000000006"), Name = "Trip Management", Description = "Trip estimation and planning", CreatedDate = DateTime.UtcNow },
            new Module { ModuleId = Guid.Parse("********-0000-0000-0000-000000000007"), Name = "Document Management", Description = "Document storage and management", CreatedDate = DateTime.UtcNow },
            new Module { ModuleId = Guid.Parse("********-0000-0000-0000-************"), Name = "Reports", Description = "Reporting and analytics", CreatedDate = DateTime.UtcNow }
        };
        modelBuilder.Entity<Module>().HasData(modules);

        // Seed entities
        var entities = new[]
        {
            new Entity { EntityId = Guid.Parse("*************-0000-0000-************"), Name = "Users", Description = "System users", CreatedDate = DateTime.UtcNow },
            new Entity { EntityId = Guid.Parse("*************-0000-0000-************"), Name = "Roles", Description = "User roles", CreatedDate = DateTime.UtcNow },
            new Entity { EntityId = Guid.Parse("*************-0000-0000-************"), Name = "Partners", Description = "Business partners", CreatedDate = DateTime.UtcNow },
            new Entity { EntityId = Guid.Parse("*************-0000-0000-000000000004"), Name = "Customers", Description = "Customer records", CreatedDate = DateTime.UtcNow },
            new Entity { EntityId = Guid.Parse("*************-0000-0000-000000000005"), Name = "Orders", Description = "Order records", CreatedDate = DateTime.UtcNow },
            new Entity { EntityId = Guid.Parse("*************-0000-0000-000000000006"), Name = "Invoices", Description = "Invoice records", CreatedDate = DateTime.UtcNow },
            new Entity { EntityId = Guid.Parse("*************-0000-0000-000000000007"), Name = "Products", Description = "Product catalog", CreatedDate = DateTime.UtcNow },
            new Entity { EntityId = Guid.Parse("*************-0000-0000-************"), Name = "Documents", Description = "Document files", CreatedDate = DateTime.UtcNow }
        };
        modelBuilder.Entity<Entity>().HasData(entities);
    }
}
