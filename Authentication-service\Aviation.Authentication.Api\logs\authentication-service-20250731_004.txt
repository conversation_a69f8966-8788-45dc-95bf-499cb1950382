2025-07-31 09:30:11.870 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-31 09:30:12.006 +05:30 [INF] Now listening on: http://localhost:5293
2025-07-31 09:30:12.014 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-07-31 09:30:12.019 +05:30 [INF] Hosting environment: Development
2025-07-31 09:30:12.023 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-07-31 09:32:11.019 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/health - null null
2025-07-31 09:32:11.130 +05:30 [WRN] Failed to determine the https port for redirect.
2025-07-31 09:32:15.003 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/health - 404 0 null 3990.421ms
2025-07-31 09:32:15.023 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5293/api/health, Response status code: 404
2025-07-31 09:32:23.904 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/health - null null
2025-07-31 09:32:23.918 +05:30 [INF] Executing endpoint 'Health checks'
2025-07-31 09:32:23.927 +05:30 [INF] Executed endpoint 'Health checks'
2025-07-31 09:32:23.933 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/health - 200 null text/plain 29.149ms
2025-07-31 09:32:47.366 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/token - application/json 81
2025-07-31 09:32:47.728 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/token - 404 0 null 361.8664ms
2025-07-31 09:32:48.029 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://localhost:5293/api/token, Response status code: 404
2025-07-31 09:33:05.994 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/clients - null null
2025-07-31 09:33:10.743 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-07-31 09:33:10.862 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-07-31 09:33:10.951 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/clients - 401 0 null 4956.2742ms
[2025-07-31 09:33:33.687 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-07-31 09:33:33.852 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:33:33.900 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:33:33.905 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:33:33.908 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:34:10.047 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/health - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFST6N23KJ:********","RequestPath":"/health","ConnectionId":"0HNEFST6N23KJ"}
[2025-07-31 09:34:10.124 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEFST6N23KJ:********","RequestPath":"/health","ConnectionId":"0HNEFST6N23KJ"}
[2025-07-31 09:34:10.161 +05:30 INF] Executing endpoint 'Health checks' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFST6N23KJ:********","RequestPath":"/health","ConnectionId":"0HNEFST6N23KJ"}
[2025-07-31 09:34:10.177 +05:30 INF] Executed endpoint 'Health checks' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFST6N23KJ:********","RequestPath":"/health","ConnectionId":"0HNEFST6N23KJ"}
[2025-07-31 09:34:10.192 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/health - 200 null text/plain 146.7809ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFST6N23KJ:********","RequestPath":"/health","ConnectionId":"0HNEFST6N23KJ"}
