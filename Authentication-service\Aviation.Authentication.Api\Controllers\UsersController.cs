using Aviation.Authentication.Api.Models;
using Aviation.Authentication.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Aviation.Authentication.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UsersController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly IAzureAdService _azureAdService;
    private readonly IAuditService _auditService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(
        IUserService userService,
        IAzureAdService azureAdService,
        IAuditService auditService,
        ILogger<UsersController> logger)
    {
        _userService = userService;
        _azureAdService = azureAdService;
        _auditService = auditService;
        _logger = logger;
    }

    /// <summary>
    /// Get all users with pagination
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<UserResponse>), 200)]
    public async Task<ActionResult<IEnumerable<UserResponse>>> GetUsers(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        if (page < 1) page = 1;
        if (pageSize < 1 || pageSize > 100) pageSize = 20;

        var users = await _userService.GetUsersAsync(page, pageSize);
        
        var response = users.Select(u => new UserResponse
        {
            UserId = u.UserId,
            EmployeeId = u.EmployeeId,
            Email = u.Email,
            FirstName = u.FirstName,
            LastName = u.LastName,
            FullName = u.FullName,
            IsActive = u.IsActive,
            LastLogin = u.LastLogin,
            IsLockedOut = u.IsLockedOut,
            CreatedDate = u.CreatedDate,
            Roles = u.UserRoles.Select(ur => new RoleResponse
            {
                RoleId = ur.Role.RoleId,
                RoleCode = ur.Role.RoleCode,
                Name = ur.Role.Name,
                Description = ur.Role.Description,
                IsActive = ur.Role.IsActive,
                AssignedDate = ur.AssignedDate
            }).ToList()
        });

        Response.Headers.Add("X-Page", page.ToString());
        Response.Headers.Add("X-Page-Size", pageSize.ToString());

        return Ok(response);
    }

    /// <summary>
    /// Get user by ID
    /// </summary>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(UserResponse), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<UserResponse>> GetUser(Guid id)
    {
        var user = await _userService.GetUserAsync(id);
        if (user == null)
        {
            return NotFound($"User with ID {id} not found");
        }

        var response = new UserResponse
        {
            UserId = user.UserId,
            EmployeeId = user.EmployeeId,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            IsActive = user.IsActive,
            LastLogin = user.LastLogin,
            IsLockedOut = user.IsLockedOut,
            CreatedDate = user.CreatedDate,
            Roles = user.UserRoles.Select(ur => new RoleResponse
            {
                RoleId = ur.Role.RoleId,
                RoleCode = ur.Role.RoleCode,
                Name = ur.Role.Name,
                Description = ur.Role.Description,
                IsActive = ur.Role.IsActive,
                AssignedDate = ur.AssignedDate
            }).ToList()
        };

        return Ok(response);
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(UserResponse), 201)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<UserResponse>> CreateUser([FromBody] CreateUserRequest request)
    {
        try
        {
            var createdBy = User.Identity?.Name ?? "api-admin";
            var response = await _userService.CreateUserAsync(request, createdBy);

            await _auditService.LogAsync("user_created", "users", 
                $"New user created: {request.FirstName} {request.LastName} ({request.EmployeeId})", 
                null, GetClientIpAddress(), GetUserAgent());

            return CreatedAtAction(nameof(GetUser), new { id = response.UserId }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user {Email}", request.Email);
            return StatusCode(500, "An error occurred while creating the user");
        }
    }

    /// <summary>
    /// Update an existing user
    /// </summary>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(UserResponse), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<UserResponse>> UpdateUser(Guid id, [FromBody] UpdateUserRequest request)
    {
        try
        {
            var response = await _userService.UpdateUserAsync(id, request);
            if (response == null)
            {
                return NotFound($"User with ID {id} not found");
            }

            await _auditService.LogAsync("user_updated", "users", 
                $"User updated: {response.FullName} ({response.EmployeeId})", 
                null, GetClientIpAddress(), GetUserAgent());

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {UserId}", id);
            return StatusCode(500, "An error occurred while updating the user");
        }
    }

    /// <summary>
    /// Delete a user (soft delete)
    /// </summary>
    [HttpDelete("{id}")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> DeleteUser(Guid id)
    {
        try
        {
            var success = await _userService.DeleteUserAsync(id);
            if (!success)
            {
                return NotFound($"User with ID {id} not found");
            }

            await _auditService.LogAsync("user_deleted", "users", 
                $"User deleted: ID {id}", null, GetClientIpAddress(), GetUserAgent());

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId}", id);
            return StatusCode(500, "An error occurred while deleting the user");
        }
    }

    /// <summary>
    /// Activate a user
    /// </summary>
    [HttpPost("{id}/activate")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> ActivateUser(Guid id)
    {
        var success = await _userService.ActivateUserAsync(id);
        if (!success)
        {
            return NotFound($"User with ID {id} not found");
        }

        await _auditService.LogAsync("user_activated", "users", 
            $"User activated: ID {id}", null, GetClientIpAddress(), GetUserAgent());

        return Ok(new { message = "User activated successfully" });
    }

    /// <summary>
    /// Deactivate a user
    /// </summary>
    [HttpPost("{id}/deactivate")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> DeactivateUser(Guid id)
    {
        var success = await _userService.DeactivateUserAsync(id);
        if (!success)
        {
            return NotFound($"User with ID {id} not found");
        }

        await _auditService.LogAsync("user_deactivated", "users", 
            $"User deactivated: ID {id}", null, GetClientIpAddress(), GetUserAgent());

        return Ok(new { message = "User deactivated successfully" });
    }

    /// <summary>
    /// Lock a user account
    /// </summary>
    [HttpPost("{id}/lock")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> LockUser(Guid id, [FromBody] LockUserRequest request)
    {
        var lockoutDuration = TimeSpan.FromMinutes(request.LockoutMinutes);
        var success = await _userService.LockUserAsync(id, lockoutDuration, request.Reason);
        
        if (!success)
        {
            return NotFound($"User with ID {id} not found");
        }

        await _auditService.LogAsync("user_locked", "users", 
            $"User locked: ID {id}, Duration: {request.LockoutMinutes} minutes, Reason: {request.Reason}", 
            null, GetClientIpAddress(), GetUserAgent());

        return Ok(new { message = $"User locked for {request.LockoutMinutes} minutes" });
    }

    /// <summary>
    /// Unlock a user account
    /// </summary>
    [HttpPost("{id}/unlock")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> UnlockUser(Guid id)
    {
        var success = await _userService.UnlockUserAsync(id);
        if (!success)
        {
            return NotFound($"User with ID {id} not found");
        }

        await _auditService.LogAsync("user_unlocked", "users", 
            $"User unlocked: ID {id}", null, GetClientIpAddress(), GetUserAgent());

        return Ok(new { message = "User unlocked successfully" });
    }

    /// <summary>
    /// Get user permissions
    /// </summary>
    [HttpGet("{id}/permissions")]
    [ProducesResponseType(typeof(UserPermissionResponse), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<UserPermissionResponse>> GetUserPermissions(Guid id)
    {
        var user = await _userService.GetUserAsync(id);
        if (user == null)
        {
            return NotFound($"User with ID {id} not found");
        }

        var permissions = await _userService.GetUserPermissionsAsync(id);

        var response = new UserPermissionResponse
        {
            UserId = user.UserId,
            EmployeeId = user.EmployeeId,
            FullName = user.FullName,
            Permissions = permissions
        };

        return Ok(response);
    }

    /// <summary>
    /// Sync user from Azure AD
    /// </summary>
    [HttpPost("sync-from-azure-ad")]
    [ProducesResponseType(typeof(UserSyncResult), 200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<UserSyncResult>> SyncFromAzureAd([FromBody] UserSyncRequest request)
    {
        try
        {
            var result = new UserSyncResult
            {
                SyncStarted = DateTime.UtcNow
            };

            IEnumerable<AzureAdUser> azureUsers;

            if (request.SyncAllUsers)
            {
                azureUsers = await _azureAdService.GetUsersAsync("accountEnabled eq true");
            }
            else if (request.SpecificEmails.Any())
            {
                azureUsers = new List<AzureAdUser>();
                foreach (var email in request.SpecificEmails)
                {
                    var azureUser = await _azureAdService.GetUserByEmailAsync(email);
                    if (azureUser != null)
                        ((List<AzureAdUser>)azureUsers).Add(azureUser);
                }
            }
            else
            {
                return BadRequest("Either SyncAllUsers must be true or SpecificEmails must be provided");
            }

            foreach (var azureUser in azureUsers)
            {
                try
                {
                    result.TotalProcessed++;

                    // Check if user already exists
                    var existingUser = await _userService.GetUserByAzureAdObjectIdAsync(azureUser.ObjectId);

                    if (existingUser != null)
                    {
                        if (request.UpdateExistingUsers)
                        {
                            var updateRequest = new UpdateUserRequest
                            {
                                FirstName = azureUser.GivenName,
                                LastName = azureUser.Surname,
                                Email = azureUser.Email,
                                IsActive = azureUser.AccountEnabled
                            };

                            await _userService.UpdateUserAsync(existingUser.UserId, updateRequest);
                            result.Updated++;
                        }
                        else
                        {
                            result.Skipped++;
                        }
                    }
                    else if (request.CreateMissingUsers)
                    {
                        var createRequest = new CreateUserRequest
                        {
                            EmployeeId = azureUser.EmployeeId,
                            Email = azureUser.Email,
                            AzureAdObjectId = azureUser.ObjectId,
                            FirstName = azureUser.GivenName,
                            LastName = azureUser.Surname
                        };

                        await _userService.CreateUserAsync(createRequest, "azure-ad-sync");
                        result.Created++;
                    }
                    else
                    {
                        result.Skipped++;
                    }
                }
                catch (Exception ex)
                {
                    result.Errors++;
                    result.ErrorMessages.Add($"Error processing user {azureUser.Email}: {ex.Message}");
                    _logger.LogError(ex, "Error syncing user {Email} from Azure AD", azureUser.Email);
                }
            }

            result.SyncCompleted = DateTime.UtcNow;

            await _auditService.LogAsync("azure_ad_sync", "users", 
                $"Azure AD sync completed: {result.Created} created, {result.Updated} updated, {result.Errors} errors", 
                null, GetClientIpAddress(), GetUserAgent());

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Azure AD sync");
            return StatusCode(500, "An error occurred during Azure AD sync");
        }
    }

    private string GetClientIpAddress()
    {
        return Request.Headers["X-Forwarded-For"].FirstOrDefault() 
               ?? Request.Headers["X-Real-IP"].FirstOrDefault()
               ?? Request.HttpContext.Connection.RemoteIpAddress?.ToString() 
               ?? "unknown";
    }

    private string GetUserAgent()
    {
        return Request.Headers.UserAgent.ToString();
    }
}

// Additional DTOs
public class LockUserRequest
{
    public int LockoutMinutes { get; set; } = 30;
    public string Reason { get; set; } = string.Empty;
}
