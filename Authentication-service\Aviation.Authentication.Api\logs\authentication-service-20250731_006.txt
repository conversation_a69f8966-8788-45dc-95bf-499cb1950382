2025-07-31 09:35:37.994 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-31 09:35:38.131 +05:30 [INF] Now listening on: http://localhost:5293
2025-07-31 09:35:38.166 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-07-31 09:35:38.169 +05:30 [INF] Hosting environment: Development
2025-07-31 09:35:38.177 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-07-31 09:36:17.865 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/AzureAdTest/test-connection - null null
2025-07-31 09:36:17.919 +05:30 [WRN] Failed to determine the https port for redirect.
2025-07-31 09:36:17.952 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)'
2025-07-31 09:36:17.991 +05:30 [INF] Route matched with {action = "TestConnection", controller = "AzureAdTest"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] TestConnection() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api).
2025-07-31 09:36:22.588 +05:30 [INF] Testing Azure AD connection...
2025-07-31 09:36:22.626 +05:30 [INF] Getting Azure AD users with filter: none
2025-07-31 09:38:06.003 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/health - null null
2025-07-31 09:38:06.018 +05:30 [INF] Executing endpoint 'Health checks'
2025-07-31 09:38:06.099 +05:30 [INF] Executed endpoint 'Health checks'
2025-07-31 09:38:06.111 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/health - 200 null text/plain 107.4709ms
[2025-07-31 09:39:46.615 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-07-31 09:39:46.711 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:39:46.716 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:39:46.719 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:39:46.721 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 09:41:09.509 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/AzureAdTest/config - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFT13NGTNK:********","RequestPath":"/api/AzureAdTest/config","ConnectionId":"0HNEFT13NGTNK"}
[2025-07-31 09:41:09.557 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEFT13NGTNK:********","RequestPath":"/api/AzureAdTest/config","ConnectionId":"0HNEFT13NGTNK"}
[2025-07-31 09:41:09.615 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFT13NGTNK:********","RequestPath":"/api/AzureAdTest/config","ConnectionId":"0HNEFT13NGTNK"}
[2025-07-31 09:41:09.630 +05:30 INF] Route matched with {action = "GetConfig", controller = "AzureAdTest"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetConfig() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"c13a5278-e70c-4fb1-9e37-66dfa9ed4d4d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)","RequestId":"0HNEFT13NGTNK:********","RequestPath":"/api/AzureAdTest/config","ConnectionId":"0HNEFT13NGTNK"}
[2025-07-31 09:41:09.745 +05:30 INF] Getting Azure AD configuration... {"SourceContext":"Aviation.Authentication.Api.Controllers.AzureAdTestController","ActionId":"c13a5278-e70c-4fb1-9e37-66dfa9ed4d4d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)","RequestId":"0HNEFT13NGTNK:********","RequestPath":"/api/AzureAdTest/config","ConnectionId":"0HNEFT13NGTNK"}
[2025-07-31 09:41:09.754 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`7[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"c13a5278-e70c-4fb1-9e37-66dfa9ed4d4d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)","RequestId":"0HNEFT13NGTNK:********","RequestPath":"/api/AzureAdTest/config","ConnectionId":"0HNEFT13NGTNK"}
[2025-07-31 09:41:09.785 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api) in 147.2317ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEFT13NGTNK:********","RequestPath":"/api/AzureAdTest/config","ConnectionId":"0HNEFT13NGTNK"}
[2025-07-31 09:41:09.795 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFT13NGTNK:********","RequestPath":"/api/AzureAdTest/config","ConnectionId":"0HNEFT13NGTNK"}
[2025-07-31 09:41:09.803 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/AzureAdTest/config - 200 null application/json; charset=utf-8 297.2435ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFT13NGTNK:********","RequestPath":"/api/AzureAdTest/config","ConnectionId":"0HNEFT13NGTNK"}
[2025-07-31 09:41:17.400 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/AzureAdTest/test-connection - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEFT13NGTNL:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFT13NGTNL"}
[2025-07-31 09:41:17.412 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEFT13NGTNL:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFT13NGTNL"}
[2025-07-31 09:41:17.420 +05:30 INF] Route matched with {action = "TestConnection", controller = "AzureAdTest"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] TestConnection() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"3d952f8b-51ca-4972-89f2-b276b2993b8f","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFT13NGTNL:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFT13NGTNL"}
[2025-07-31 09:41:17.430 +05:30 INF] Testing Azure AD connection... {"SourceContext":"Aviation.Authentication.Api.Controllers.AzureAdTestController","ActionId":"3d952f8b-51ca-4972-89f2-b276b2993b8f","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFT13NGTNL:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFT13NGTNL"}
[2025-07-31 09:41:17.434 +05:30 INF] Getting Azure AD users with filter: none {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"3d952f8b-51ca-4972-89f2-b276b2993b8f","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEFT13NGTNL:********","RequestPath":"/api/AzureAdTest/test-connection","ConnectionId":"0HNEFT13NGTNL"}
