2025-07-31 17:04:40.421 +05:30 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-31 17:04:40.630 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-31 17:04:40.638 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-31 17:04:40.646 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-31 17:04:40.662 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-31 17:04:40.678 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-07-31 17:04:40.695 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-31 17:04:40.758 +05:30 [INF] Now listening on: http://localhost:5293
2025-07-31 17:04:40.764 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-07-31 17:04:40.769 +05:30 [INF] Hosting environment: Development
2025-07-31 17:04:40.772 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
