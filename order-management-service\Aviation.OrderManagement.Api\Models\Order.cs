using System.ComponentModel.DataAnnotations;

namespace Aviation.OrderManagement.Api.Models;

public class Order
{
    public int Id { get; set; }
    
    [Required]
    public string OrderNumber { get; set; } = string.Empty;
    
    public int CustomerId { get; set; }
    public int PartnerId { get; set; }
    
    [Required]
    public string DepartureAirport { get; set; } = string.Empty;
    
    [Required]
    public string ArrivalAirport { get; set; } = string.Empty;
    
    public DateTime DepartureDate { get; set; }
    public DateTime? ReturnDate { get; set; }
    
    public int PassengerCount { get; set; }
    public FlightType FlightType { get; set; }
    public OrderStatus Status { get; set; }
    
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = "USD";
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    
    public List<OrderItem> OrderItems { get; set; } = new();
    public List<Passenger> Passengers { get; set; } = new();
}

public class OrderItem
{
    public int Id { get; set; }
    public int OrderId { get; set; }
    public Order Order { get; set; } = null!;
    
    public string ItemType { get; set; } = string.Empty; // Flight, Hotel, Car, etc.
    public string ItemDescription { get; set; } = string.Empty;
    public decimal UnitPrice { get; set; }
    public int Quantity { get; set; }
    public decimal TotalPrice { get; set; }
    
    public string? ExternalReference { get; set; }
    public DateTime? ServiceDate { get; set; }
}

public class Passenger
{
    public int Id { get; set; }
    public int OrderId { get; set; }
    public Order Order { get; set; } = null!;
    
    [Required]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    public string LastName { get; set; } = string.Empty;
    
    public DateTime DateOfBirth { get; set; }
    public string PassportNumber { get; set; } = string.Empty;
    public string Nationality { get; set; } = string.Empty;
    public PassengerType Type { get; set; }
}

public enum FlightType
{
    OneWay,
    RoundTrip,
    MultiCity
}

public enum OrderStatus
{
    Draft,
    Pending,
    Confirmed,
    InProgress,
    Completed,
    Cancelled,
    Refunded
}

public enum PassengerType
{
    Adult,
    Child,
    Infant
}

// DTOs for API
public class CreateOrderRequest
{
    [Required]
    public int CustomerId { get; set; }
    
    [Required]
    public int PartnerId { get; set; }
    
    [Required]
    public string DepartureAirport { get; set; } = string.Empty;
    
    [Required]
    public string ArrivalAirport { get; set; } = string.Empty;
    
    [Required]
    public DateTime DepartureDate { get; set; }
    
    public DateTime? ReturnDate { get; set; }
    
    [Range(1, 20)]
    public int PassengerCount { get; set; }
    
    public FlightType FlightType { get; set; }
    
    public List<CreateOrderItemRequest> OrderItems { get; set; } = new();
    public List<CreatePassengerRequest> Passengers { get; set; } = new();
}

public class CreateOrderItemRequest
{
    [Required]
    public string ItemType { get; set; } = string.Empty;
    
    [Required]
    public string ItemDescription { get; set; } = string.Empty;
    
    [Range(0.01, double.MaxValue)]
    public decimal UnitPrice { get; set; }
    
    [Range(1, int.MaxValue)]
    public int Quantity { get; set; }
    
    public string? ExternalReference { get; set; }
    public DateTime? ServiceDate { get; set; }
}

public class CreatePassengerRequest
{
    [Required]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    public string LastName { get; set; } = string.Empty;
    
    [Required]
    public DateTime DateOfBirth { get; set; }
    
    public string PassportNumber { get; set; } = string.Empty;
    public string Nationality { get; set; } = string.Empty;
    public PassengerType Type { get; set; }
}

public class UpdateOrderStatusRequest
{
    [Required]
    public OrderStatus Status { get; set; }
    
    public string? Reason { get; set; }
}
