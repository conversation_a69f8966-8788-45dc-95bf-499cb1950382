apiVersion: apps/v1
kind: Deployment
metadata:
  name: authentication-service
  namespace: aviation-system
  labels:
    app: authentication-service
    version: v1
    component: auth-server
spec:
  replicas: 2
  selector:
    matchLabels:
      app: authentication-service
  template:
    metadata:
      labels:
        app: authentication-service
        version: v1
    spec:
      containers:
      - name: authentication-service
        image: aviation/authentication-service:latest
        ports:
        - containerPort: 80
          name: http
        - containerPort: 443
          name: https
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ASPNETCORE_URLS
          value: "http://+:80"
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: authentication-secrets
              key: database-connection-string
        - name: JWT__SecretKey
          valueFrom:
            secretKeyRef:
              name: authentication-secrets
              key: jwt-secret-key
        - name: JWT__Issuer
          valueFrom:
            configMapKeyRef:
              name: authentication-config
              key: jwt-issuer
        - name: JWT__Audience
          valueFrom:
            configMapKeyRef:
              name: authentication-config
              key: jwt-audience
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}
      securityContext:
        fsGroup: 2000
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: authentication-service
  namespace: aviation-system
  labels:
    app: authentication-service
spec:
  selector:
    app: authentication-service
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: https
    port: 443
    targetPort: 443
    protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: authentication-ingress
  namespace: aviation-system
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - auth.aviation-management.com
    secretName: authentication-tls
  rules:
  - host: auth.aviation-management.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: authentication-service
            port:
              number: 80
