# =============================================
# Database Connection Test Script
# =============================================

param(
    [Parameter(Mandatory=$true)]
    [string]$ServerName,
    
    [Parameter(Mandatory=$true)]
    [string]$Username,
    
    [Parameter(Mandatory=$true)]
    [string]$Password,
    
    [Parameter(Mandatory=$false)]
    [string]$DatabaseName = "AviationAuthentication",
    
    [Parameter(Mandatory=$false)]
    [switch]$UseWindowsAuth = $false
)

Write-Host "🔍 Testing Database Connection..." -ForegroundColor Green

# Build connection string
if ($UseWindowsAuth) {
    $connectionString = "Server=$ServerName;Database=$DatabaseName;Integrated Security=true;TrustServerCertificate=true;"
} else {
    $connectionString = "Server=$ServerName;Database=$DatabaseName;User Id=$Username;Password=$Password;TrustServerCertificate=true;Encrypt=true;"
}

try {
    Write-Host "📡 Connecting to: $ServerName" -ForegroundColor Yellow
    Write-Host "🗄️ Database: $DatabaseName" -ForegroundColor Yellow
    
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    # Test basic connectivity
    $command = New-Object System.Data.SqlClient.SqlCommand("SELECT @@VERSION", $connection)
    $version = $command.ExecuteScalar()
    
    Write-Host "✅ Connection successful!" -ForegroundColor Green
    Write-Host "📊 SQL Server Version: $version" -ForegroundColor Cyan
    
    # Test if database exists
    $command = New-Object System.Data.SqlClient.SqlCommand("SELECT DB_ID('$DatabaseName')", $connection)
    $dbId = $command.ExecuteScalar()
    
    if ($dbId) {
        Write-Host "✅ Database '$DatabaseName' exists" -ForegroundColor Green
        
        # Test if tables exist
        $command = New-Object System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'", $connection)
        $tableCount = $command.ExecuteScalar()
        
        Write-Host "📋 Tables found: $tableCount" -ForegroundColor Cyan
        
        # Test specific tables
        $tables = @("Users", "Roles", "Clients", "Scopes", "AccessTokens")
        foreach ($table in $tables) {
            $command = New-Object System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '$table'", $connection)
            $exists = $command.ExecuteScalar()
            
            if ($exists -gt 0) {
                Write-Host "   ✅ $table table exists" -ForegroundColor Green
            } else {
                Write-Host "   ❌ $table table missing" -ForegroundColor Red
            }
        }
        
        # Test sample data
        $command = New-Object System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM Roles", $connection)
        $roleCount = $command.ExecuteScalar()
        Write-Host "👥 Roles in database: $roleCount" -ForegroundColor Cyan
        
        $command = New-Object System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM Scopes", $connection)
        $scopeCount = $command.ExecuteScalar()
        Write-Host "🔐 Scopes in database: $scopeCount" -ForegroundColor Cyan
        
    } else {
        Write-Host "❌ Database '$DatabaseName' does not exist" -ForegroundColor Red
    }
    
    $connection.Close()
    
    Write-Host ""
    Write-Host "🔗 Connection String:" -ForegroundColor Cyan
    Write-Host "$connectionString" -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ Connection failed: $($_.Exception.Message)" -ForegroundColor Red
    
    Write-Host ""
    Write-Host "🔧 Troubleshooting Tips:" -ForegroundColor Cyan
    Write-Host "   1. Verify server name and port" -ForegroundColor White
    Write-Host "   2. Check username and password" -ForegroundColor White
    Write-Host "   3. Ensure SQL Server is running" -ForegroundColor White
    Write-Host "   4. Check firewall settings" -ForegroundColor White
    Write-Host "   5. Verify SQL Server authentication mode" -ForegroundColor White
    
    exit 1
}

Write-Host ""
Write-Host "✅ Connection test completed successfully!" -ForegroundColor Green
