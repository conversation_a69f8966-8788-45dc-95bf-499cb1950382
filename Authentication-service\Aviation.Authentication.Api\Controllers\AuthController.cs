using Aviation.Authentication.Api.Models;
using Aviation.Authentication.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Aviation.Authentication.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly IUserTokenService _userTokenService;
    private readonly IAzureAdService _azureAdService;
    private readonly IAuditService _auditService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(
        IUserService userService,
        IUserTokenService userTokenService,
        IAzureAdService azureAdService,
        IAuditService auditService,
        ILogger<AuthController> logger)
    {
        _userService = userService;
        _userTokenService = userTokenService;
        _azureAdService = azureAdService;
        _auditService = auditService;
        _logger = logger;
    }

    /// <summary>
    /// User login with Azure AD token or credentials
    /// </summary>
    [HttpPost("login")]
    [ProducesResponseType(typeof(UserLoginResponse), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<UserLoginResponse>> Login([FromBody] UserLoginRequest request)
    {
        var ipAddress = GetClientIpAddress();
        var userAgent = GetUserAgent();

        try
        {
            User? user = null;
            AzureAdUser? azureAdUser = null;

            // If Azure AD token is provided, validate it
            if (!string.IsNullOrEmpty(request.AzureAdToken))
            {
                azureAdUser = await _azureAdService.ValidateTokenAsync(request.AzureAdToken);
                if (azureAdUser == null)
                {
                    // Determine if input is email or employee ID for proper logging
                    string? attemptedEmail = request.EmailOrEmployeeId.Contains("@") ? request.EmailOrEmployeeId : null;
                    string? attemptedEmployeeId = !request.EmailOrEmployeeId.Contains("@") ? request.EmailOrEmployeeId : null;

                    await _userService.LogLoginAttemptAsync(null, attemptedEmail, attemptedEmployeeId, ipAddress, false, "Invalid Azure AD token");
                    return Unauthorized(new { error = "Invalid Azure AD token" });
                }

                // Find user by Azure AD Object ID
                user = await _userService.GetUserByAzureAdObjectIdAsync(azureAdUser.ObjectId);
                
                // If user doesn't exist, try to find by email
                if (user == null && !string.IsNullOrEmpty(azureAdUser.Email))
                {
                    user = await _userService.GetUserByEmailAsync(azureAdUser.Email);
                    
                    // Update Azure AD Object ID if user found by email
                    if (user != null)
                    {
                        var updateRequest = new UpdateUserRequest
                        {
                            FirstName = azureAdUser.GivenName,
                            LastName = azureAdUser.Surname
                        };
                        await _userService.UpdateUserAsync(user.UserId, updateRequest);
                    }
                }
            }
            else
            {
                // Try to find user by email or employee ID
                if (request.EmailOrEmployeeId.Contains("@"))
                {
                    user = await _userService.GetUserByEmailAsync(request.EmailOrEmployeeId);
                }
                else
                {
                    user = await _userService.GetUserByEmployeeIdAsync(request.EmailOrEmployeeId);
                }
            }

            if (user == null)
            {
                // Determine if input is email or employee ID for proper logging
                string? attemptedEmail = request.EmailOrEmployeeId.Contains("@") ? request.EmailOrEmployeeId : null;
                string? attemptedEmployeeId = !request.EmailOrEmployeeId.Contains("@") ? request.EmailOrEmployeeId : null;

                await _userService.LogLoginAttemptAsync(null, attemptedEmail, attemptedEmployeeId, ipAddress, false, "User not found");
                return Unauthorized(new { error = "User not found" });
            }

            if (!user.IsActive)
            {
                await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, false, "User account is inactive");
                return Unauthorized(new { error = "User account is inactive" });
            }

            if (user.IsLockedOut)
            {
                await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, false, "User account is locked");
                return Unauthorized(new { error = "User account is locked" });
            }

            // Get user permissions
            var permissions = await _userService.GetUserPermissionsAsync(user.UserId);

            // Generate JWT token for user
            var accessToken = await _userTokenService.GenerateUserTokenAsync(user, permissions);

            // Update last login
            user.LastLogin = DateTime.UtcNow;
            user.FailedAttempts = 0;
            await _userService.UpdateUserAsync(user.UserId, new UpdateUserRequest());

            // Log successful login
            await _userService.LogLoginAttemptAsync(user.UserId, user.Email, user.EmployeeId, ipAddress, true);

            var userResponse = new UserResponse
            {
                UserId = user.UserId,
                EmployeeId = user.EmployeeId,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                FullName = user.FullName,
                IsActive = user.IsActive,
                LastLogin = user.LastLogin,
                IsLockedOut = user.IsLockedOut,
                CreatedDate = user.CreatedDate,
                Roles = user.UserRoles.Select(ur => new RoleResponse
                {
                    RoleId = ur.Role.RoleId,
                    RoleCode = ur.Role.RoleCode,
                    Name = ur.Role.Name,
                    Description = ur.Role.Description,
                    IsActive = ur.Role.IsActive,
                    AssignedDate = ur.AssignedDate
                }).ToList()
            };

            return Ok(new UserLoginResponse
            {
                AccessToken = accessToken,
                TokenType = "Bearer",
                ExpiresIn = 28800, // 8 hours
                User = userResponse,
                Permissions = permissions
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during user login for {EmailOrEmployeeId}", request.EmailOrEmployeeId);

            // Determine if input is email or employee ID for proper logging
            string? attemptedEmail = request.EmailOrEmployeeId.Contains("@") ? request.EmailOrEmployeeId : null;
            string? attemptedEmployeeId = !request.EmailOrEmployeeId.Contains("@") ? request.EmailOrEmployeeId : null;

            await _userService.LogLoginAttemptAsync(null, attemptedEmail, attemptedEmployeeId, ipAddress, false, "Login error");
            return StatusCode(500, new { error = "An error occurred during login" });
        }
    }

    /// <summary>
    /// Validate user token
    /// </summary>
    [HttpPost("validate")]
    [ProducesResponseType(typeof(UserTokenValidationResult), 200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<UserTokenValidationResult>> ValidateToken([FromBody] ValidateTokenRequest request)
    {
        try
        {
            var result = await _userTokenService.ValidateUserTokenAsync(request.Token);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating user token");
            return BadRequest(new { error = "Token validation failed" });
        }
    }

    /// <summary>
    /// Refresh user token
    /// </summary>
    [HttpPost("refresh")]
    [Authorize]
    [ProducesResponseType(typeof(RefreshTokenResponse), 200)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<RefreshTokenResponse>> RefreshToken()
    {
        try
        {
            var userIdClaim = User.FindFirst("sub")?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized(new { error = "Invalid token" });
            }

            var user = await _userService.GetUserAsync(userId);
            if (user == null || !user.IsActive || user.IsLockedOut)
            {
                return Unauthorized(new { error = "User not found or inactive" });
            }

            // Get updated permissions
            var permissions = await _userService.GetUserPermissionsAsync(userId);

            // Generate new token
            var accessToken = await _userTokenService.GenerateUserTokenAsync(user, permissions);

            return Ok(new RefreshTokenResponse
            {
                AccessToken = accessToken,
                TokenType = "Bearer",
                ExpiresIn = 28800 // 8 hours
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing user token");
            return StatusCode(500, new { error = "Token refresh failed" });
        }
    }

    /// <summary>
    /// Logout user (revoke token)
    /// </summary>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(200)]
    public async Task<IActionResult> Logout()
    {
        try
        {
            var jtiClaim = User.FindFirst("jti")?.Value;
            if (!string.IsNullOrEmpty(jtiClaim))
            {
                await _userTokenService.RevokeUserTokenAsync(jtiClaim);
            }

            var userIdClaim = User.FindFirst("sub")?.Value;
            if (Guid.TryParse(userIdClaim, out var userId))
            {
                await _auditService.LogAsync("user_logout", "auth", 
                    $"User logged out: {userId}", null, GetClientIpAddress(), GetUserAgent());
            }

            return Ok(new { message = "Logged out successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            return StatusCode(500, new { error = "Logout failed" });
        }
    }

    /// <summary>
    /// Get current user profile
    /// </summary>
    [HttpGet("profile")]
    [Authorize]
    [ProducesResponseType(typeof(UserResponse), 200)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<UserResponse>> GetProfile()
    {
        try
        {
            var userIdClaim = User.FindFirst("sub")?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized(new { error = "Invalid token" });
            }

            var user = await _userService.GetUserAsync(userId);
            if (user == null)
            {
                return Unauthorized(new { error = "User not found" });
            }

            var response = new UserResponse
            {
                UserId = user.UserId,
                EmployeeId = user.EmployeeId,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                FullName = user.FullName,
                IsActive = user.IsActive,
                LastLogin = user.LastLogin,
                IsLockedOut = user.IsLockedOut,
                CreatedDate = user.CreatedDate,
                Roles = user.UserRoles.Select(ur => new RoleResponse
                {
                    RoleId = ur.Role.RoleId,
                    RoleCode = ur.Role.RoleCode,
                    Name = ur.Role.Name,
                    Description = ur.Role.Description,
                    IsActive = ur.Role.IsActive,
                    AssignedDate = ur.AssignedDate
                }).ToList()
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user profile");
            return StatusCode(500, new { error = "Failed to get profile" });
        }
    }

    private string GetClientIpAddress()
    {
        return Request.Headers["X-Forwarded-For"].FirstOrDefault() 
               ?? Request.Headers["X-Real-IP"].FirstOrDefault()
               ?? Request.HttpContext.Connection.RemoteIpAddress?.ToString() 
               ?? "unknown";
    }

    private string GetUserAgent()
    {
        return Request.Headers.UserAgent.ToString();
    }
}

// Additional DTOs
public class ValidateTokenRequest
{
    public string Token { get; set; } = string.Empty;
}

public class RefreshTokenResponse
{
    public string AccessToken { get; set; } = string.Empty;
    public string TokenType { get; set; } = "Bearer";
    public int ExpiresIn { get; set; }
}
