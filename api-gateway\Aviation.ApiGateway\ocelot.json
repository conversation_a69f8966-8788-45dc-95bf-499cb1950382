{"Routes": [{"DownstreamPathTemplate": "/api/partners/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5001}], "UpstreamPathTemplate": "/api/v1/partners/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["partner:read", "partner:write"]}}, {"DownstreamPathTemplate": "/api/customers/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5001}], "UpstreamPathTemplate": "/api/v1/customers/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["customer:read", "customer:write"]}}, {"DownstreamPathTemplate": "/api/orders/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5002}], "UpstreamPathTemplate": "/api/v1/orders/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["order:read", "order:write", "order:cancel"]}}, {"DownstreamPathTemplate": "/api/invoices/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5003}], "UpstreamPathTemplate": "/api/v1/invoices/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["invoice:read", "invoice:write", "billing:read"]}}, {"DownstreamPathTemplate": "/api/products/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5004}], "UpstreamPathTemplate": "/api/v1/products/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["product:read", "product:write"]}}, {"DownstreamPathTemplate": "/api/pricing/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5004}], "UpstreamPathTemplate": "/api/v1/pricing/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["pricing:read", "pricing:write"]}}, {"DownstreamPathTemplate": "/api/trip-estimation/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5005}], "UpstreamPathTemplate": "/api/v1/trip-estimation/{everything}", "UpstreamHttpMethod": ["GET", "POST"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["trip:estimate"]}}, {"DownstreamPathTemplate": "/api/flights/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5005}], "UpstreamPathTemplate": "/api/v1/flights/{everything}", "UpstreamHttpMethod": ["GET"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["flight:read"]}}, {"DownstreamPathTemplate": "/api/airports/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5005}], "UpstreamPathTemplate": "/api/v1/airports/{everything}", "UpstreamHttpMethod": ["GET"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["airport:read"]}}, {"DownstreamPathTemplate": "/api/documents/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5006}], "UpstreamPathTemplate": "/api/v1/documents/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["document:read", "document:write", "document:delete"]}}, {"DownstreamPathTemplate": "/api/app-registry/{everything}", "DownstreamScheme": "https", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5007}], "UpstreamPathTemplate": "/api/v1/app-registry/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"], "AuthenticationOptions": {"AuthenticationProviderKey": "Bearer", "AllowedScopes": ["app:read", "app:write", "app:admin"]}}], "GlobalConfiguration": {"BaseUrl": "https://api.aviation-management.com", "RequestIdKey": "X-Request-ID"}}