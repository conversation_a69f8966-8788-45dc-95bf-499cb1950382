{"info": {"_postman_id": "aviation-auth-service-collection", "name": "Aviation Authentication Service", "description": "Complete API collection for the Aviation Authentication Service - OAuth 2.0 B2B Authentication and User Management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "aviation-auth"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}, "response": []}, {"name": "OAuth 2.0 Endpoints", "item": [{"name": "Get OAuth Discovery Document", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/oauth/.well-known/oauth-authorization-server", "host": ["{{baseUrl}}"], "path": ["o<PERSON>h", ".well-known", "oauth-authorization-server"]}}, "response": []}, {"name": "Get Access Token (Client Credentials)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.access_token);", "    pm.environment.set('token_type', response.token_type);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "text"}, {"key": "client_id", "value": "{{client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{client_secret}}", "type": "text"}, {"key": "scope", "value": "{{scopes}}", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/oauth/token", "host": ["{{baseUrl}}"], "path": ["o<PERSON>h", "token"]}}, "response": []}, {"name": "Token Introspection", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "token", "value": "{{access_token}}", "type": "text"}, {"key": "token_type_hint", "value": "access_token", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/oauth/introspect", "host": ["{{baseUrl}}"], "path": ["o<PERSON>h", "introspect"]}}, "response": []}, {"name": "Token Revocation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "token", "value": "{{access_token}}", "type": "text"}, {"key": "token_type_hint", "value": "access_token", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/oauth/revoke", "host": ["{{baseUrl}}"], "path": ["o<PERSON>h", "revoke"]}}, "response": []}, {"name": "Get JWKS (JSON Web Key Set)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/oauth/.well-known/jwks.json", "host": ["{{baseUrl}}"], "path": ["o<PERSON>h", ".well-known", "jwks.json"]}}, "response": []}]}, {"name": "User Authentication", "item": [{"name": "User <PERSON>gin (Azure AD)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('user_token', response.token);", "    pm.environment.set('user_id', response.user.userId);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"azureAdToken\": \"{{azure_ad_token}}\",\n    \"email\": \"{{user_email}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "response": []}, {"name": "<PERSON><PERSON> (Credentials)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('user_token', response.token);", "    pm.environment.set('user_id', response.user.userId);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{user_email}}\",\n    \"password\": \"{{user_password}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "response": []}, {"name": "Validate User Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"token\": \"{{user_token}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/validate", "host": ["{{baseUrl}}"], "path": ["api", "auth", "validate"]}}, "response": []}, {"name": "Refresh User Token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "auth", "refresh"]}}, "response": []}, {"name": "User <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}}, "response": []}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}}, "response": []}]}, {"name": "Client Management", "item": [{"name": "Get All Clients", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/clients?page=1&pageSize=20", "host": ["{{baseUrl}}"], "path": ["api", "clients"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "20"}]}}, "response": []}, {"name": "Get Client by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/clients/{{client_db_id}}", "host": ["{{baseUrl}}"], "path": ["api", "clients", "{{client_db_id}}"]}}, "response": []}, {"name": "Create New Client", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('client_id', response.clientId);", "    pm.environment.set('client_secret', response.clientSecret);", "    pm.environment.set('client_db_id', response.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"clientName\": \"Test Partner API Client\",\n    \"description\": \"Test client for partner integration\",\n    \"clientType\": \"Partner\",\n    \"scopes\": [\"partner:read\", \"partner:write\"],\n    \"accessTokenLifetimeSeconds\": 3600,\n    \"rateLimitPerHour\": 1000\n}"}, "url": {"raw": "{{baseUrl}}/api/clients", "host": ["{{baseUrl}}"], "path": ["api", "clients"]}}, "response": []}, {"name": "Update Client", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"clientName\": \"Updated Partner API Client\",\n    \"description\": \"Updated test client for partner integration\",\n    \"status\": \"Active\",\n    \"accessTokenLifetimeSeconds\": 7200,\n    \"rateLimitPerHour\": 2000\n}"}, "url": {"raw": "{{baseUrl}}/api/clients/{{client_db_id}}", "host": ["{{baseUrl}}"], "path": ["api", "clients", "{{client_db_id}}"]}}, "response": []}, {"name": "Deactivate Client", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/clients/{{client_db_id}}/deactivate", "host": ["{{baseUrl}}"], "path": ["api", "clients", "{{client_db_id}}", "deactivate"]}}, "response": []}, {"name": "Regenerate Client Secret", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/clients/{{client_db_id}}/regenerate-secret", "host": ["{{baseUrl}}"], "path": ["api", "clients", "{{client_db_id}}", "regenerate-secret"]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/users?page=1&pageSize=20", "host": ["{{baseUrl}}"], "path": ["api", "users"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "20"}]}}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/users/{{user_id}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{user_id}}"]}}, "response": []}, {"name": "Create New User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"employeeId\": \"EMP001\",\n    \"department\": \"Operations\",\n    \"jobTitle\": \"Flight Coordinator\",\n    \"phoneNumber\": \"******-0123\",\n    \"isActive\": true,\n    \"roleIds\": [\"role-guid-here\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}}, "response": []}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"department\": \"Operations\",\n    \"jobTitle\": \"Senior Flight Coordinator\",\n    \"phoneNumber\": \"******-0124\",\n    \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{user_id}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{user_id}}"]}}, "response": []}, {"name": "Activate User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/users/{{user_id}}/activate", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{user_id}}", "activate"]}}, "response": []}, {"name": "Deactivate User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/users/{{user_id}}/deactivate", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{user_id}}", "deactivate"]}}, "response": []}, {"name": "Assign Role to User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"roleId\": \"{{role_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{user_id}}/roles", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{user_id}}", "roles"]}}, "response": []}, {"name": "Remove Role from User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/users/{{user_id}}/roles/{{role_id}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{user_id}}", "roles", "{{role_id}}"]}}, "response": []}, {"name": "Sync Users from Azure AD", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"syncAllUsers\": false,\n    \"specificEmails\": [\"<EMAIL>\", \"<EMAIL>\"],\n    \"updateExistingUsers\": true,\n    \"createMissingUsers\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/users/sync-azure-ad", "host": ["{{baseUrl}}"], "path": ["api", "users", "sync-azure-ad"]}}, "response": []}]}, {"name": "Role Management", "item": [{"name": "Get All Roles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/roles?activeOnly=true", "host": ["{{baseUrl}}"], "path": ["api", "roles"], "query": [{"key": "activeOnly", "value": "true"}]}}, "response": []}, {"name": "Get Role by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/roles/{{role_id}}", "host": ["{{baseUrl}}"], "path": ["api", "roles", "{{role_id}}"]}}, "response": []}, {"name": "Create New Role", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Custom Role\",\n    \"description\": \"Custom role for specific operations\",\n    \"isActive\": true,\n    \"permissionIds\": [\"permission-guid-1\", \"permission-guid-2\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/roles", "host": ["{{baseUrl}}"], "path": ["api", "roles"]}}, "response": []}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Custom Role\",\n    \"description\": \"Updated custom role description\",\n    \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/roles/{{role_id}}", "host": ["{{baseUrl}}"], "path": ["api", "roles", "{{role_id}}"]}}, "response": []}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/roles/{{role_id}}", "host": ["{{baseUrl}}"], "path": ["api", "roles", "{{role_id}}"]}}, "response": []}]}, {"name": "Scope Management", "item": [{"name": "Get All Scopes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/scopes", "host": ["{{baseUrl}}"], "path": ["api", "scopes"]}}, "response": []}, {"name": "Get Scope by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/scopes/{{scope_id}}", "host": ["{{baseUrl}}"], "path": ["api", "scopes", "{{scope_id}}"]}}, "response": []}, {"name": "Create <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"custom:read\",\n    \"description\": \"Read access to custom resources\",\n    \"category\": \"Custom\",\n    \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/scopes", "host": ["{{baseUrl}}"], "path": ["api", "scopes"]}}, "response": []}, {"name": "Update Scope", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"custom:read\",\n    \"description\": \"Updated read access to custom resources\",\n    \"category\": \"Custom\",\n    \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/scopes/{{scope_id}}", "host": ["{{baseUrl}}"], "path": ["api", "scopes", "{{scope_id}}"]}}, "response": []}, {"name": "Delete Scope", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/scopes/{{scope_id}}", "host": ["{{baseUrl}}"], "path": ["api", "scopes", "{{scope_id}}"]}}, "response": []}, {"name": "Get Scopes by Category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{baseUrl}}/api/scopes/by-category", "host": ["{{baseUrl}}"], "path": ["api", "scopes", "by-category"]}}, "response": []}]}, {"name": "Azure AD Integration Tests", "item": [{"name": "Get Azure AD Configuration", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/azureadtest/config", "host": ["{{baseUrl}}"], "path": ["api", "azureadtest", "config"]}}, "response": []}, {"name": "Test Azure AD Service", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/azureadtest/test-service", "host": ["{{baseUrl}}"], "path": ["api", "azureadtest", "test-service"]}}, "response": []}, {"name": "Test Azure AD Connection", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/azureadtest/test-connection", "host": ["{{baseUrl}}"], "path": ["api", "azureadtest", "test-connection"]}}, "response": []}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5293", "type": "string"}, {"key": "client_id", "value": "your-client-id", "type": "string"}, {"key": "client_secret", "value": "your-client-secret", "type": "string"}, {"key": "client_db_id", "value": "1", "type": "string"}, {"key": "scopes", "value": "partner:read partner:write", "type": "string"}, {"key": "user_email", "value": "<EMAIL>", "type": "string"}, {"key": "user_password", "value": "password123", "type": "string"}, {"key": "user_id", "value": "user-guid-here", "type": "string"}, {"key": "role_id", "value": "role-guid-here", "type": "string"}, {"key": "scope_id", "value": "scope-id-here", "type": "string"}, {"key": "azure_ad_token", "value": "azure-ad-access-token", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "user_token", "value": "", "type": "string"}, {"key": "token_type", "value": "Bearer", "type": "string"}]}