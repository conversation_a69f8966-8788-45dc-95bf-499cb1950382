2025-07-31 14:52:49.210 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-31 14:52:49.459 +05:30 [INF] Now listening on: http://localhost:5293
2025-07-31 14:52:49.521 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-07-31 14:52:49.524 +05:30 [INF] Hosting environment: Development
2025-07-31 14:52:49.528 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-07-31 14:53:20.641 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-connection - null null
2025-07-31 14:53:20.706 +05:30 [WRN] Failed to determine the https port for redirect.
2025-07-31 14:53:25.320 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)'
2025-07-31 14:53:25.358 +05:30 [INF] Route matched with {action = "TestConnection", controller = "AzureAdTest"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] TestConnection() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api).
2025-07-31 14:53:29.461 +05:30 [INF] Testing Azure AD connection...
2025-07-31 14:53:29.471 +05:30 [INF] Getting Azure AD users with filter: none
2025-07-31 14:53:32.225 +05:30 [ERR] Error getting Azure AD users
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUsersAsync(String filter) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 154
2025-07-31 14:53:32.341 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType5`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-31 14:53:32.414 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api) in 7040.2805ms
2025-07-31 14:53:32.444 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)'
2025-07-31 14:53:32.457 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-connection - 200 null application/json; charset=utf-8 11817.5536ms
2025-07-31 14:53:56.846 +05:30 [INF] Application is shutting down...
[2025-07-31 14:54:18.265 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-07-31 14:54:19.374 +05:30 INF] Now listening on: http://localhost:5293 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 14:54:19.385 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 14:54:19.391 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 14:54:19.395 +05:30 INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-07-31 14:54:26.364 +05:30 INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - application/json 93 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
[2025-07-31 14:54:26.512 +05:30 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
[2025-07-31 14:54:26.894 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
[2025-07-31 14:54:27.014 +05:30 INF] Route matched with {action = "SyncFromAzureAd", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Services.UserSyncResult]] SyncFromAzureAd(Aviation.Authentication.Api.Services.UserSyncRequest) on controller Aviation.Authentication.Api.Controllers.UsersController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"52277c7a-aa5c-4b33-9da0-2643e17936cd","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
[2025-07-31 14:54:28.964 +05:30 INF] Getting Azure AD users with filter: accountEnabled eq true {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"52277c7a-aa5c-4b33-9da0-2643e17936cd","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
[2025-07-31 14:54:31.048 +05:30 ERR] Error getting Azure AD users {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"52277c7a-aa5c-4b33-9da0-2643e17936cd","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUsersAsync(String filter) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 154
[2025-07-31 14:54:38.064 +05:30 INF] Executed DbCommand (292ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"52277c7a-aa5c-4b33-9da0-2643e17936cd","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
[2025-07-31 14:54:38.279 +05:30 INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Services.UserSyncResult'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"52277c7a-aa5c-4b33-9da0-2643e17936cd","ActionName":"Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
[2025-07-31 14:54:38.369 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api) in 11311.3215ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
[2025-07-31 14:54:38.393 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
[2025-07-31 14:54:38.432 +05:30 INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - 200 null application/json; charset=utf-8 12070.1967ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG2G5JNTN6:********","RequestPath":"/api/users/sync-from-azure-ad","ConnectionId":"0HNEG2G5JNTN6"}
[2025-07-31 14:55:27.768 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/config - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:27.832 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:27.940 +05:30 INF] Route matched with {action = "GetConfig", controller = "AzureAdTest"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetConfig() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"2466089d-5b68-4da7-b7c3-a66b56f4801d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:28.124 +05:30 INF] Getting Azure AD configuration... {"SourceContext":"Aviation.Authentication.Api.Controllers.AzureAdTestController","ActionId":"2466089d-5b68-4da7-b7c3-a66b56f4801d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:28.212 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`7[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"2466089d-5b68-4da7-b7c3-a66b56f4801d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:28.269 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api) in 180.3437ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:28.303 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:28.336 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/config - 200 null application/json; charset=utf-8 567.6605ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/config","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:33.008 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-service - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:33.098 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:33.123 +05:30 INF] Route matched with {action = "TestService", controller = "AzureAdTest"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult TestService() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"39a8405f-e07d-44ee-b2cc-d14f67f241ac","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:33.169 +05:30 INF] Testing Azure AD service instantiation... {"SourceContext":"Aviation.Authentication.Api.Controllers.AzureAdTestController","ActionId":"39a8405f-e07d-44ee-b2cc-d14f67f241ac","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:33.206 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType4`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"39a8405f-e07d-44ee-b2cc-d14f67f241ac","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:33.337 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api) in 179.1197ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:33.384 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:33.415 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-service - 200 null application/json; charset=utf-8 407.7683ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-service","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:36.907 +05:30 INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-connection - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:36.972 +05:30 INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:37.046 +05:30 INF] Route matched with {action = "TestConnection", controller = "AzureAdTest"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] TestConnection() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"24bfad44-015c-4d3a-b618-361357128e1d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:37.103 +05:30 INF] Testing Azure AD connection... {"SourceContext":"Aviation.Authentication.Api.Controllers.AzureAdTestController","ActionId":"24bfad44-015c-4d3a-b618-361357128e1d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:37.123 +05:30 INF] Getting Azure AD users with filter: none {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"24bfad44-015c-4d3a-b618-361357128e1d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:37.749 +05:30 ERR] Error getting Azure AD users {"SourceContext":"Aviation.Authentication.Api.Services.AzureAdService","ActionId":"24bfad44-015c-4d3a-b618-361357128e1d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEG2G5JNTN8"}
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUsersAsync(String filter) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 154
[2025-07-31 14:55:37.797 +05:30 INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType5`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"24bfad44-015c-4d3a-b618-361357128e1d","ActionName":"Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:37.865 +05:30 INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api) in 773.2244ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:37.887 +05:30 INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 14:55:37.915 +05:30 INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-connection - 200 null application/json; charset=utf-8 1008.782ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNEG2G5JNTN8:********","RequestPath":"/api/azureadtest/test-connection","ConnectionId":"0HNEG2G5JNTN8"}
[2025-07-31 15:00:07.129 +05:30 INF] Application is shutting down... {"SourceContext":"Microsoft.Hosting.Lifetime"}
