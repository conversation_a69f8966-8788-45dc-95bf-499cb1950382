{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Data Source=DESKTOP-MR5E5LR;Initial Catalog=AviationAuthentication;Integrated Security=True;TrustServerCertificate=True;Connection Timeout=30;"}, "JWT": {"SecretKey": "dev-aviation-auth-secret-key-256-bits-minimum-length-required", "Issuer": "https://localhost:8080", "Audience": "aviation-api-dev", "ExpirationMinutes": 60}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}}}