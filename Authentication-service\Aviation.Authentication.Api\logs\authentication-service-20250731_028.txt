2025-07-31 14:54:18.265 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-31 14:54:19.374 +05:30 [INF] Now listening on: http://localhost:5293
2025-07-31 14:54:19.385 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-07-31 14:54:19.391 +05:30 [INF] Hosting environment: Development
2025-07-31 14:54:19.395 +05:30 [INF] Content root path: K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api
2025-07-31 14:54:26.364 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - application/json 93
2025-07-31 14:54:26.512 +05:30 [WRN] Failed to determine the https port for redirect.
2025-07-31 14:54:26.894 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)'
2025-07-31 14:54:27.014 +05:30 [INF] Route matched with {action = "SyncFromAzureAd", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Aviation.Authentication.Api.Services.UserSyncResult]] SyncFromAzureAd(Aviation.Authentication.Api.Services.UserSyncRequest) on controller Aviation.Authentication.Api.Controllers.UsersController (Aviation.Authentication.Api).
2025-07-31 14:54:28.964 +05:30 [INF] Getting Azure AD users with filter: accountEnabled eq true
2025-07-31 14:54:31.048 +05:30 [ERR] Error getting Azure AD users
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUsersAsync(String filter) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 154
2025-07-31 14:54:38.064 +05:30 [INF] Executed DbCommand (292ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (DbType = Int32), @p2='?' (Size = 2000), @p3='?' (Size = 1000), @p4='?' (Size = 50), @p5='?' (Size = 200), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 500)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [AuditLogs] ([Action], [ClientId], [Details], [ErrorMessage], [IpAddress], [Resource], [Success], [Timestamp], [UserAgent])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-07-31 14:54:38.279 +05:30 [INF] Executing OkObjectResult, writing value of type 'Aviation.Authentication.Api.Services.UserSyncResult'.
2025-07-31 14:54:38.369 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api) in 11311.3215ms
2025-07-31 14:54:38.393 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.UsersController.SyncFromAzureAd (Aviation.Authentication.Api)'
2025-07-31 14:54:38.432 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5293/api/users/sync-from-azure-ad - 200 null application/json; charset=utf-8 12070.1967ms
2025-07-31 14:55:27.768 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/config - null null
2025-07-31 14:55:27.832 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)'
2025-07-31 14:55:27.940 +05:30 [INF] Route matched with {action = "GetConfig", controller = "AzureAdTest"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetConfig() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api).
2025-07-31 14:55:28.124 +05:30 [INF] Getting Azure AD configuration...
2025-07-31 14:55:28.212 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`7[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-31 14:55:28.269 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api) in 180.3437ms
2025-07-31 14:55:28.303 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.GetConfig (Aviation.Authentication.Api)'
2025-07-31 14:55:28.336 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/config - 200 null application/json; charset=utf-8 567.6605ms
2025-07-31 14:55:33.008 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-service - null null
2025-07-31 14:55:33.098 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)'
2025-07-31 14:55:33.123 +05:30 [INF] Route matched with {action = "TestService", controller = "AzureAdTest"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult TestService() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api).
2025-07-31 14:55:33.169 +05:30 [INF] Testing Azure AD service instantiation...
2025-07-31 14:55:33.206 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType4`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-31 14:55:33.337 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api) in 179.1197ms
2025-07-31 14:55:33.384 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestService (Aviation.Authentication.Api)'
2025-07-31 14:55:33.415 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-service - 200 null application/json; charset=utf-8 407.7683ms
2025-07-31 14:55:36.907 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-connection - null null
2025-07-31 14:55:36.972 +05:30 [INF] Executing endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)'
2025-07-31 14:55:37.046 +05:30 [INF] Route matched with {action = "TestConnection", controller = "AzureAdTest"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] TestConnection() on controller Aviation.Authentication.Api.Controllers.AzureAdTestController (Aviation.Authentication.Api).
2025-07-31 14:55:37.103 +05:30 [INF] Testing Azure AD connection...
2025-07-31 14:55:37.123 +05:30 [INF] Getting Azure AD users with filter: none
2025-07-31 14:55:37.749 +05:30 [ERR] Error getting Azure AD users
Microsoft.Graph.Models.ODataErrors.ODataError: Insufficient privileges to complete the operation.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponse(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.UsersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at Aviation.Authentication.Api.Services.AzureAdService.GetUsersAsync(String filter) in K:\Aviation Management System\Authentication-service\Aviation.Authentication.Api\Services\AzureAdService.cs:line 154
2025-07-31 14:55:37.797 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType5`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-31 14:55:37.865 +05:30 [INF] Executed action Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api) in 773.2244ms
2025-07-31 14:55:37.887 +05:30 [INF] Executed endpoint 'Aviation.Authentication.Api.Controllers.AzureAdTestController.TestConnection (Aviation.Authentication.Api)'
2025-07-31 14:55:37.915 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5293/api/azureadtest/test-connection - 200 null application/json; charset=utf-8 1008.782ms
2025-07-31 15:00:07.129 +05:30 [INF] Application is shutting down...
