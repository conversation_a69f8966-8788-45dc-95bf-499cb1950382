using Aviation.OrderManagement.Api.Models;
using Microsoft.EntityFrameworkCore;

namespace Aviation.OrderManagement.Api.Data;

public class OrderDbContext : DbContext
{
    public OrderDbContext(DbContextOptions<OrderDbContext> options) : base(options)
    {
    }

    public DbSet<Order> Orders { get; set; }
    public DbSet<OrderItem> OrderItems { get; set; }
    public DbSet<Passenger> Passengers { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Order entity
        modelBuilder.Entity<Order>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrderNumber).IsRequired().HasMaxLength(50);
            entity.HasIndex(e => e.OrderNumber).IsUnique();
            
            entity.Property(e => e.DepartureAirport).IsRequired().HasMaxLength(10);
            entity.Property(e => e.ArrivalAirport).IsRequired().HasMaxLength(10);
            entity.Property(e => e.Currency).HasMaxLength(3).HasDefaultValue("USD");
            entity.Property(e => e.CreatedBy).HasMaxLength(100);
            
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
            
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            // Configure relationships
            entity.HasMany(e => e.OrderItems)
                  .WithOne(e => e.Order)
                  .HasForeignKey(e => e.OrderId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasMany(e => e.Passengers)
                  .WithOne(e => e.Order)
                  .HasForeignKey(e => e.OrderId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure OrderItem entity
        modelBuilder.Entity<OrderItem>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ItemType).IsRequired().HasMaxLength(50);
            entity.Property(e => e.ItemDescription).IsRequired().HasMaxLength(500);
            entity.Property(e => e.ExternalReference).HasMaxLength(100);
            
            entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");
            entity.Property(e => e.TotalPrice).HasColumnType("decimal(18,2)");
        });

        // Configure Passenger entity
        modelBuilder.Entity<Passenger>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.PassportNumber).HasMaxLength(20);
            entity.Property(e => e.Nationality).HasMaxLength(50);
        });

        // Seed data
        SeedData(modelBuilder);
    }

    private void SeedData(ModelBuilder modelBuilder)
    {
        // Seed sample orders
        modelBuilder.Entity<Order>().HasData(
            new Order
            {
                Id = 1,
                OrderNumber = "ORD-2024-001",
                CustomerId = 1,
                PartnerId = 1,
                DepartureAirport = "JFK",
                ArrivalAirport = "LAX",
                DepartureDate = DateTime.UtcNow.AddDays(30),
                PassengerCount = 2,
                FlightType = FlightType.RoundTrip,
                Status = OrderStatus.Confirmed,
                TotalAmount = 1200.00m,
                Currency = "USD",
                CreatedAt = DateTime.UtcNow.AddDays(-5),
                UpdatedAt = DateTime.UtcNow.AddDays(-5),
                CreatedBy = "system"
            },
            new Order
            {
                Id = 2,
                OrderNumber = "ORD-2024-002",
                CustomerId = 2,
                PartnerId = 2,
                DepartureAirport = "LHR",
                ArrivalAirport = "CDG",
                DepartureDate = DateTime.UtcNow.AddDays(15),
                PassengerCount = 1,
                FlightType = FlightType.OneWay,
                Status = OrderStatus.Pending,
                TotalAmount = 450.00m,
                Currency = "EUR",
                CreatedAt = DateTime.UtcNow.AddDays(-2),
                UpdatedAt = DateTime.UtcNow.AddDays(-2),
                CreatedBy = "api-client"
            }
        );

        // Seed sample order items
        modelBuilder.Entity<OrderItem>().HasData(
            new OrderItem
            {
                Id = 1,
                OrderId = 1,
                ItemType = "Flight",
                ItemDescription = "JFK to LAX - Economy Class",
                UnitPrice = 600.00m,
                Quantity = 2,
                TotalPrice = 1200.00m,
                ExternalReference = "FL-001",
                ServiceDate = DateTime.UtcNow.AddDays(30)
            },
            new OrderItem
            {
                Id = 2,
                OrderId = 2,
                ItemType = "Flight",
                ItemDescription = "LHR to CDG - Business Class",
                UnitPrice = 450.00m,
                Quantity = 1,
                TotalPrice = 450.00m,
                ExternalReference = "FL-002",
                ServiceDate = DateTime.UtcNow.AddDays(15)
            }
        );

        // Seed sample passengers
        modelBuilder.Entity<Passenger>().HasData(
            new Passenger
            {
                Id = 1,
                OrderId = 1,
                FirstName = "John",
                LastName = "Doe",
                DateOfBirth = new DateTime(1985, 5, 15),
                PassportNumber = "US123456789",
                Nationality = "USA",
                Type = PassengerType.Adult
            },
            new Passenger
            {
                Id = 2,
                OrderId = 1,
                FirstName = "Jane",
                LastName = "Doe",
                DateOfBirth = new DateTime(1987, 8, 22),
                PassportNumber = "US987654321",
                Nationality = "USA",
                Type = PassengerType.Adult
            },
            new Passenger
            {
                Id = 3,
                OrderId = 2,
                FirstName = "Pierre",
                LastName = "Martin",
                DateOfBirth = new DateTime(1975, 3, 10),
                PassportNumber = "FR456789123",
                Nationality = "France",
                Type = PassengerType.Adult
            }
        );
    }
}
