using Aviation.Authentication.Api.Models;
using Aviation.Authentication.Api.Data;
using Microsoft.EntityFrameworkCore;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using System.Text;
using Microsoft.Graph;
using Azure.Identity;
using Microsoft.Extensions.Options;

namespace Aviation.Authentication.Api.Services;

public class AzureAdService : IAzureAdService
{
    private readonly ILogger<AzureAdService> _logger;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly AzureAdConfiguration _azureAdOptions;

    public AzureAdService(ILogger<AzureAdService> logger, IOptions<AzureAdConfiguration> azureAdOptions)
    {
        _logger = logger;
        _azureAdOptions = azureAdOptions.Value;

        var options = new ClientSecretCredentialOptions
        {
            AuthorityHost = AzureAuthorityHosts.AzurePublicCloud,
        };

        var clientSecretCredential = new ClientSecretCredential(
            _azureAdOptions.TenantId,
            _azureAdOptions.ClientId,
            _azureAdOptions.ClientSecret,
            options);

        _graphServiceClient = new GraphServiceClient(clientSecretCredential);
    }

    public async Task<AzureAdUser?> ValidateTokenAsync(string accessToken)
    {
        try
        {
            _logger.LogInformation("Validating Azure AD token");

            // Get user info from Microsoft Graph using the access token
            var user = await _graphServiceClient.Me.GetAsync();

            if (user == null)
            {
                _logger.LogWarning("Failed to get user information from Microsoft Graph");
                return null;
            }

            return new AzureAdUser
            {
                ObjectId = Guid.Parse(user.Id!),
                DisplayName = user.DisplayName ?? "",
                Email = user.Mail ?? user.UserPrincipalName ?? "",
                UserPrincipalName = user.UserPrincipalName ?? "",
                GivenName = user.GivenName ?? "",
                Surname = user.Surname ?? "",
                JobTitle = user.JobTitle ?? "",
                Department = user.Department ?? "",
                OfficeLocation = user.OfficeLocation ?? ""
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Azure AD token");
            return null;
        }
    }

    public async Task<AzureAdUser?> GetUserByObjectIdAsync(Guid objectId)
    {
        try
        {
            _logger.LogInformation("Getting Azure AD user by Object ID: {ObjectId}", objectId);

            var user = await _graphServiceClient.Users[objectId.ToString()].GetAsync();

            if (user == null)
            {
                _logger.LogWarning("User not found in Azure AD: {ObjectId}", objectId);
                return null;
            }

            return new AzureAdUser
            {
                ObjectId = Guid.Parse(user.Id!),
                DisplayName = user.DisplayName ?? "",
                Email = user.Mail ?? user.UserPrincipalName ?? "",
                UserPrincipalName = user.UserPrincipalName ?? "",
                GivenName = user.GivenName ?? "",
                Surname = user.Surname ?? "",
                JobTitle = user.JobTitle ?? "",
                Department = user.Department ?? "",
                OfficeLocation = user.OfficeLocation ?? ""
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Azure AD user by Object ID: {ObjectId}", objectId);
            return null;
        }
    }

    public async Task<AzureAdUser?> GetUserByEmailAsync(string email)
    {
        try
        {
            _logger.LogInformation("Getting Azure AD user by email: {Email}", email);

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter = $"mail eq '{email}' or userPrincipalName eq '{email}'";
                    requestConfiguration.QueryParameters.Top = 1;
                });

            var user = users?.Value?.FirstOrDefault();
            if (user == null)
            {
                _logger.LogWarning("User not found in Azure AD: {Email}", email);
                return null;
            }

            return new AzureAdUser
            {
                ObjectId = Guid.Parse(user.Id!),
                DisplayName = user.DisplayName ?? "",
                Email = user.Mail ?? user.UserPrincipalName ?? "",
                UserPrincipalName = user.UserPrincipalName ?? "",
                GivenName = user.GivenName ?? "",
                Surname = user.Surname ?? "",
                JobTitle = user.JobTitle ?? "",
                Department = user.Department ?? "",
                OfficeLocation = user.OfficeLocation ?? ""
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Azure AD user by email: {Email}", email);
            return null;
        }
    }

    public async Task<IEnumerable<AzureAdUser>> GetUsersAsync(string? filter = null)
    {
        try
        {
            _logger.LogInformation("Getting Azure AD users with filter: {Filter}", filter ?? "none");

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    if (!string.IsNullOrEmpty(filter))
                    {
                        requestConfiguration.QueryParameters.Filter = filter;
                    }
                    requestConfiguration.QueryParameters.Top = 100; // Limit to 100 users
                });

            if (users?.Value == null)
            {
                return Enumerable.Empty<AzureAdUser>();
            }

            return users.Value.Select(user => new AzureAdUser
            {
                ObjectId = Guid.Parse(user.Id!),
                DisplayName = user.DisplayName ?? "",
                Email = user.Mail ?? user.UserPrincipalName ?? "",
                UserPrincipalName = user.UserPrincipalName ?? "",
                GivenName = user.GivenName ?? "",
                Surname = user.Surname ?? "",
                JobTitle = user.JobTitle ?? "",
                Department = user.Department ?? "",
                OfficeLocation = user.OfficeLocation ?? ""
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Azure AD users");
            return Enumerable.Empty<AzureAdUser>();
        }
    }

    public async Task<bool> IsUserInGroupAsync(Guid userId, string groupName)
    {
        try
        {
            _logger.LogInformation("Checking if user {UserId} is in group: {GroupName}", userId, groupName);

            var memberOf = await _graphServiceClient.Users[userId.ToString()].MemberOf
                .GetAsync();

            if (memberOf?.Value == null)
            {
                return false;
            }

            return memberOf.Value.Any(group =>
                group is Microsoft.Graph.Models.Group g &&
                (g.DisplayName?.Equals(groupName, StringComparison.OrdinalIgnoreCase) == true ||
                 g.MailNickname?.Equals(groupName, StringComparison.OrdinalIgnoreCase) == true));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if user {UserId} is in group: {GroupName}", userId, groupName);
            return false;
        }
    }

    public async Task<IEnumerable<string>> GetUserGroupsAsync(Guid userId)
    {
        try
        {
            _logger.LogInformation("Getting groups for user: {UserId}", userId);

            var memberOf = await _graphServiceClient.Users[userId.ToString()].MemberOf
                .GetAsync();

            if (memberOf?.Value == null)
            {
                return Enumerable.Empty<string>();
            }

            return memberOf.Value
                .OfType<Microsoft.Graph.Models.Group>()
                .Where(g => !string.IsNullOrEmpty(g.DisplayName))
                .Select(g => g.DisplayName!)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting groups for user: {UserId}", userId);
            return Enumerable.Empty<string>();
        }
    }
}

public class UserTokenService : IUserTokenService
{
    private readonly IConfiguration _configuration;
    private readonly AuthDbContext _context;
    private readonly ILogger<UserTokenService> _logger;

    public UserTokenService(IConfiguration configuration, AuthDbContext context, ILogger<UserTokenService> logger)
    {
        _configuration = configuration;
        _context = context;
        _logger = logger;
    }

    public async Task<string> GenerateUserTokenAsync(Aviation.Authentication.Api.Models.User user, List<string> permissions)
    {
        var key = Encoding.UTF8.GetBytes(_configuration["JWT:SecretKey"] ?? throw new InvalidOperationException("JWT secret key not configured"));
        var tokenHandler = new JwtSecurityTokenHandler();

        var tokenId = Guid.NewGuid().ToString();
        var issuedAt = DateTime.UtcNow;
        var expiresAt = issuedAt.AddHours(8); // 8-hour token for users

        var claims = new List<Claim>
        {
            new(JwtRegisteredClaimNames.Jti, tokenId),
            new(JwtRegisteredClaimNames.Sub, user.UserId.ToString()),
            new(JwtRegisteredClaimNames.Email, user.Email),
            new("employee_id", user.EmployeeId),
            new("given_name", user.FirstName),
            new("family_name", user.LastName),
            new("azure_ad_object_id", user.AzureAdObjectId.ToString()),
            new("token_type", "user")
        };

        // Add role claims
        foreach (var userRole in user.UserRoles)
        {
            claims.Add(new Claim("role", userRole.Role.Name));
            claims.Add(new Claim("role_code", userRole.Role.RoleCode));
        }

        // Add permission claims
        foreach (var permission in permissions)
        {
            claims.Add(new Claim("permission", permission));
        }

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = expiresAt,
            Issuer = _configuration["JWT:Issuer"],
            Audience = _configuration["JWT:Audience"],
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        // Store token in database for tracking
        // Use the test client (Id = 1) for user tokens since ClientId cannot be null
        var accessToken = new AccessToken
        {
            TokenId = tokenId,
            ClientId = 1, // Use test client for user tokens
            TokenHash = ComputeTokenHash(tokenString),
            IssuedAt = issuedAt,
            ExpiresAt = expiresAt,
            IpAddress = "",
            UserAgent = "",
            Scopes = string.Join(",", permissions)
        };

        _context.AccessTokens.Add(accessToken);
        await _context.SaveChangesAsync();

        return tokenString;
    }

    public async Task<UserTokenValidationResult> ValidateUserTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_configuration["JWT:SecretKey"] ?? throw new InvalidOperationException("JWT secret key not configured"));

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _configuration["JWT:Issuer"],
                ValidateAudience = true,
                ValidAudience = _configuration["JWT:Audience"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
            
            if (validatedToken is JwtSecurityToken jwtToken)
            {
                var jti = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti)?.Value;
                var tokenType = jwtToken.Claims.FirstOrDefault(x => x.Type == "token_type")?.Value;
                
                if (tokenType != "user")
                {
                    return new UserTokenValidationResult { IsValid = false, ErrorMessage = "Invalid token type" };
                }

                if (!string.IsNullOrEmpty(jti))
                {
                    // Check if token is revoked
                    var storedToken = await _context.AccessTokens
                        .FirstOrDefaultAsync(t => t.TokenId == jti && !t.IsRevoked);
                    
                    if (storedToken == null || storedToken.ExpiresAt <= DateTime.UtcNow)
                    {
                        return new UserTokenValidationResult { IsValid = false, ErrorMessage = "Token not found or expired" };
                    }

                    var userIdClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Sub)?.Value;
                    var employeeIdClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == "employee_id")?.Value;
                    var permissions = jwtToken.Claims.Where(x => x.Type == "permission").Select(x => x.Value).ToList();
                    var roles = jwtToken.Claims.Where(x => x.Type == "role").Select(x => x.Value).ToList();

                    return new UserTokenValidationResult
                    {
                        IsValid = true,
                        UserId = Guid.TryParse(userIdClaim, out var userId) ? userId : null,
                        EmployeeId = employeeIdClaim,
                        Permissions = permissions,
                        Roles = roles,
                        ExpiresAt = storedToken.ExpiresAt
                    };
                }
            }

            return new UserTokenValidationResult { IsValid = false, ErrorMessage = "Invalid token format" };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "User token validation failed");
            return new UserTokenValidationResult { IsValid = false, ErrorMessage = "Token validation failed" };
        }
    }

    public async Task<bool> RevokeUserTokenAsync(string tokenId)
    {
        try
        {
            var token = await _context.AccessTokens
                .FirstOrDefaultAsync(t => t.TokenId == tokenId && !t.IsRevoked);

            if (token != null)
            {
                token.IsRevoked = true;
                token.RevokedAt = DateTime.UtcNow;
                token.RevokedReason = "User token revoked";

                await _context.SaveChangesAsync();
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking user token {TokenId}", tokenId);
            return false;
        }
    }

    private static string ComputeTokenHash(string token)
    {
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(token));
        return Convert.ToBase64String(hashBytes);
    }
}
